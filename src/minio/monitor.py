import subprocess
import time
import logging

logging.basicConfig(
    filename="monitor.log", level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

python_script = "easyocr_remove_watermark_function.py"  # 替换为你的 Python 脚本文件名
max_retries = 100  # 最大重试次数
retry_delay = 2  # 重试延迟（秒）

for attempt in range(max_retries):
    try:
        logging.info(f"Starting Python script (Attempt {attempt + 1})...")
        print(f"Starting Python script (Attempt {attempt + 1})...")
        subprocess.run(
            [
                "python3",
                python_script,
                "-i",
                "/mnt/2t_data/minio_cache/original_files/edu-xunkao-raw/exams/images/sampled_images/frame_rate_0.5/期末考试-20241231-江西-南昌",
                "-o",
                "/mnt/2t_data/minio_cache/output/edu-xunkao-raw/exams/images/sampled_images/frame_rate_0.5/期末考试-20241231-江西-南昌",
                "-s",
                "-skip"
            ],
            check=True,
        )
        logging.info("Script executed successfully.")
        break  # 如果脚本成功运行并退出，停止重试
    except subprocess.CalledProcessError as e:
        print(f"Script crashed with error: {e}")
        logging.error(f"Script crashed with error: {e}")
        if attempt < max_retries - 1:
            print(f"Retrying in {retry_delay} seconds...")
            logging.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
        else:
            print("Max retries reached. Exiting monitor script.")
            logging.error("Max retries reached. Exiting monitor script.")
