from concurrent.futures import ProcessPoolExecutor, as_completed
import os
from tqdm import tqdm
from minio_dir_count import connect_minio
import logger


# 设置日志记录器
logger = logger.setup_logger(base_dir="/home/<USER>/data_engineer/src/minio")


# 并发下载; 不能在方法里面传minio因为不可序列化
def download_file(object, save_path, minio_client):
    dir_path = os.path.dirname(object.object_name) + "/"  # 添加斜杠以确保路径完整
    CACHE_FILE_PATH = os.path.join(save_path, dir_path)
    os.makedirs(CACHE_FILE_PATH, exist_ok=True)
    try:
        local_path = os.path.join(CACHE_FILE_PATH, os.path.basename(object.object_name))
        if not os.path.exists(local_path):
            minio_client.fget_object(
                bucket_name=object.bucket_name, object_name=object.object_name, file_path=local_path
            )
    except:
        logger.error(f"文件下载失败{object.object_name}")


if __name__ == "__main__":
    BUCKET_NAME = "edu-xunkao-raw"
    CACHE_ROOT_PATH = "/mnt/2t_data/minio_cache/original_files"
    CACHE_BUCKET_PATH = os.path.join(CACHE_ROOT_PATH, BUCKET_NAME)
    os.makedirs(CACHE_BUCKET_PATH, exist_ok=True)
    minio_client = connect_minio()
    objects = minio_client.list_objects(BUCKET_NAME, recursive=True)
    for obj in tqdm(objects, desc="处理文件中", unit="文件", ncols=100):
        download_file(object=obj, save_path=CACHE_BUCKET_PATH, minio_client=minio_client)
