import logging
import os
import sys


def setup_logger(base_dir: str):
    # 获取调用脚本的文件名
    caller_script_name = os.path.basename(sys.argv[0]).split(".")[0]
    log_file_name = f"{caller_script_name}.log"
    log_file_path = os.path.join(base_dir, log_file_name)

    # 创建日志记录器
    logger = logging.getLogger("__name__")
    logger.setLevel(logging.INFO)  # 设置日志级别

    # 创建文件处理器，将日志写入文件
    file_handler = logging.FileHandler(log_file_path, mode="a", encoding="utf-8")
    file_handler.setLevel(logging.INFO)  # 设置文件处理器的日志级别

    # 创建日志格式
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(formatter)  # 为处理器设置格式

    # 将处理器添加到日志记录器
    logger.addHandler(file_handler)

    return logger
