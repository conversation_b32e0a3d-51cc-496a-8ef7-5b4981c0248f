import os
from pathlib import Path

from minio import Minio
from directory_encoder import encode_single_directory
from minio_dir_count import connect_minio
import logger
from minio_progress  import Progress

# 设置日志记录器
logger = logger.setup_logger(base_dir="/home/<USER>/data_engineer/src/minio")


def is_chinese_dir(dir_name: str):
    """原始目录名，格式如：期末考试-20241231-江西-南昌，进行加密处理"""
    return any("\u4e00" <= char <= "\u9fff" for char in dir_name)


def generate_sensitive_directories_mapping_dict(root_dir: str) -> dict:
    res = {}
    """递归查找敏感信息目录【只会以中文命名】"""
    for dirpath, dirnames, files in os.walk(root_dir, topdown=False):
        for dirname in dirnames:
            if is_chinese_dir(dirname):
                desensitive_folder_name = encode_single_directory(dirname)
                if desensitive_folder_name:
                    res[dirname] = desensitive_folder_name
    return res



def upload_directory_to_minio(
    source_dir: str, bucket_name: str, minio_client: Minio, encode_mapping_dict: dict = None
):
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            content_type = 'video/mp4' if file.endswith(".mp4") else "image/jpeg"
            file_path = os.path.join(root, file)
            relative_path = os.path.relpath(file_path, source_dir)
            if encode_mapping_dict:
                relative_path = Path(relative_path)
                parts = list(relative_path.parts)
                # 只有可能四级或者五级目录有敏感信息; 如果有映射关系，则映射；否则保持原样
                parts[3] = encode_mapping_dict.get(parts[3], parts[3])
                parts[4] = encode_mapping_dict.get(parts[4], parts[4])
                relative_path = Path(*parts)  
            # 构造 MinIO 中的对象路径
            object_name = os.path.join(relative_path)
            # 上传文件; 视频文件用多个并行
            if content_type == 'video/mp4':
                minio_client.fput_object(bucket_name, object_name, file_path, progress=Progress(),content_type=content_type, num_parallel_uploads=10)
            else:
                minio_client.fput_object(bucket_name, object_name, file_path, progress=Progress(),content_type=content_type)
            logger.info(f"文件 {file_path} 已上传到 {bucket_name}/{object_name}")


if __name__ == "__main__":
    FILE_PATH = "/mnt/2t_data/minio_cache/output/edu-xunkao-raw"
    TARGET_BUCKET = "edu-xunkao-desensitized"
    dict = generate_sensitive_directories_mapping_dict(FILE_PATH)
    minio_client = connect_minio()
    upload_directory_to_minio(FILE_PATH, TARGET_BUCKET, minio_client, dict)
