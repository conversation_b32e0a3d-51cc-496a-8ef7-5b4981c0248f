"""
Filename: easyocr_remove_watermark_function.py
Author: 关桥
Date: 2025-01-25
Version: 3.0
Description: 敏感图片、视频脱敏脚本
Dependencies: 1. ffmpeg(https://ffmpeg.org/download.html)
              2. torch(https://pytorch.org/get-started/locally/, https://download.pytorch.org/whl/torch_stable.html)
              3. EasyOCR model
"""

from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import cv2
import subprocess
import argparse
import easyocr
from pathlib import Path
from functools import reduce
from tqdm import tqdm

import logger

# 设置日志记录器
logger = logger.setup_logger(base_dir="/home/<USER>/data_engineer/src/minio")
areas_to_judge = [
    ((0, 0), (1300, 120)),  # 左上角时间
    # ((900, 0), (1300, 120)),  # 右上角位置
    ((0, 600), (1300, 750)),  # 下方摄像头名称
]


def run_ffmpeg_command(command):
    # print("Executing FFmpeg command:", " ".join(command))
    subprocess.run(command, check=True)


def build_ffmpeg_command(
    frame_size,
    input_path,
    output_path,
    text_regions,
    remove_type="gblur",
    sigma=1,
    is_image=True,
):

    command = ["ffmpeg", "-y", "-i", str(input_path)]

    filter_complex = []
    video_width, video_height = frame_size

    base_video_label = "[0:v]"  # 初始输入是原始视频流
    overlay_complex = []
    last_overlay_label = base_video_label
    for i, region in enumerate(text_regions):
        (x1, y1), (x2, y2) = region
        w = min(x2 - x1, video_width - x1)  # 确保宽度不超出视频宽度
        h = min(y2 - y1, video_height - y1)  # 确保高度不超出视频高度

        if w <= 2 or h <= 2:
            continue  # 如果裁剪区域无效，则跳过此区域

        # 创建一个唯一的标签名用于每个模糊区域
        crop_label = f"[crop{i}]"
        blur_label = f"[blur{i}]"
        overlay_label = f"[overlay{i}]"

        # 构建裁剪和模糊滤镜链
        crop_blur_filter = f"{base_video_label}crop={w}:{h}:{x1}:{y1}{crop_label};{crop_label}gblur=sigma={sigma}{blur_label}"
        filter_complex.append(crop_blur_filter)

        # 构建叠加滤镜链
        overlay_filter = f"{last_overlay_label}{blur_label}overlay={x1}:{y1}{overlay_label}"
        overlay_complex.append(overlay_filter)
        last_overlay_label = overlay_label

    for overlay_complex_single in overlay_complex:
        filter_complex.append(overlay_complex_single)

    # 将所有过滤器链接起来，并最终映射到输出视频流
    final_filter = ";".join(filter_complex)
    command.extend(
        [
            "-loglevel",
            "error",
        ]
    )
    if final_filter:
        command.extend(
            [
                "-filter_complex",
                final_filter,
                "-map",
                last_overlay_label,
            ]
        )
    if is_image:
        command.extend(
            [
                str(output_path),
            ]
        )
    else:
        command.extend(
            [
                "-c:v",
                "libx264",
                "-preset",
                "veryslow",
                "-x264-params",
                "crf=23:threads=16",
                str(output_path),
            ]
        )

    return command


def is_region_within_any_bounds(region, bounding_boxes):
    """
    检查 region 是否完全包含在 bounding_boxes 中的任意一个区域内。

    参数:
    - region: (top_left, bottom_right)，文本区域的左上角和右下角坐标。
    - bounding_boxes: [(top_left, bottom_right), ...]，
    多个限定区域的列表，每个元素为一个区域的左上角和右下角坐标。

    返回:
    - bool: 如果 region 完全在任意一个 bounding_box 内，则返回 True；否则返回 False。
    """
    for bounding_box in bounding_boxes:
        (bx1, by1), (bx2, by2) = bounding_box
        (x1, y1), (x2, y2) = region

        if bx1 <= x1 and by1 <= y1 and bx2 >= x2 and by2 >= y2:
            return True
    return False


def find_text_region(frame, reader, bounding_boxes=None, image_size=(1280, 720)):
    # 使用EasyOCR检测文字区域
    results = reader.readtext(frame, text_threshold=0.2, min_size=4)

    # 提取文字区域的坐标
    text_regions = []
    for bbox, text, prob in results:
        (top_left, top_right, bottom_right, bottom_left) = bbox
        top_left = tuple(map(int, top_left))
        bottom_right = tuple(map(int, bottom_right))
        text_regions.append((top_left, bottom_right))

    # 过滤文本区域，保留那些位于 bounding_boxes 中任意一个区域内的区域（如果提供了 bounding_boxes）
    if bounding_boxes:
        text_regions = [
            region for region in text_regions if is_region_within_any_bounds(region, bounding_boxes)
        ]

    # 合并重叠或相邻的文本区域
    merged_regions = merge_adjacent_regions(text_regions)
    filtered_regions = []
    for region in merged_regions:
        if ((region[1][0] - region[0][0]) >= 3 * (region[1][1] - region[0][1])) and (
            region[1][0] - region[0][0]
        ) * (region[1][1] - region[0][1]) > 1000 * (image_size[0] * image_size[1]) / (1280 * 720.0):
            new_region = (
                (max(0, region[0][0] - 50), region[0][1]),
                (min(image_size[0], region[1][0] + 50), region[1][1]),
            )
            filtered_regions.append(new_region)

    return filtered_regions


def merge_adjacent_regions(regions, y_threshold=10, side_threshold=300):
    def center_y(region):
        (x1, y1), (x2, y2) = region
        return (y1 + y2) // 2

    def closest_side_distance(region1, region2):
        (x1_1, y1_1), (x2_1, y2_1) = region1
        (x1_2, y1_2), (x2_2, y2_2) = region2

        # 计算两个框最接近边的距离
        if x2_1 < x1_2:  # 框1在框2左边
            return x1_2 - x2_1
        elif x2_2 < x1_1:  # 框2在框1左边
            return x1_1 - x2_2
        else:  # 两个框水平上有交集
            return 0

    def merge_two_regions(region1, region2):
        (x1_1, y1_1), (x2_1, y2_1) = region1
        (x1_2, y1_2), (x2_2, y2_2) = region2
        return (
            (min(x1_1, x1_2), min(y1_1, y1_2)),
            (max(x2_1, x2_2), max(y2_1, y2_2)),
        )

    regions = sorted(regions, key=lambda r: center_y(r))  # 按照中心点的y轴排序
    merged = []

    while regions:
        current_region = regions.pop(0)
        merged_with_current = [current_region]

        i = 0
        while i < len(regions):
            region = regions[i]
            if (
                abs(center_y(current_region) - center_y(region)) <= y_threshold
                and closest_side_distance(current_region, region) <= side_threshold
            ):
                merged_with_current.append(region)
                regions.pop(i)
            else:
                i += 1

        if len(merged_with_current) > 1:
            merged.append(
                reduce(
                    lambda r1, r2: merge_two_regions(r1, r2),
                    merged_with_current,
                )
            )
        else:
            merged.append(current_region)

    return merged


def adjust_areas_to_resolution(areas_to_remove, original_resolution, target_resolution=(1280, 720)):
    """根据原始分辨率和目标分辨率调整要去除水印的区域"""
    original_width, original_height = original_resolution
    target_width, target_height = target_resolution

    width_ratio = float(original_width) / target_width
    height_ratio = float(original_height) / target_height

    adjusted_areas = []
    for area in areas_to_remove:
        top_left, bottom_right = area
        adjusted_top_left = (
            int(top_left[0] * width_ratio),
            int(top_left[1] * height_ratio),
        )
        adjusted_bottom_right = (
            int(bottom_right[0] * width_ratio),
            int(bottom_right[1] * height_ratio),
        )
        adjusted_areas.append((adjusted_top_left, adjusted_bottom_right))

    return adjusted_areas


def remove_watermark_for_single_image(
    image_path,
    output_path,
    easyocr_reader,
    remove_type="gblur",
    original_filename=True,
    **kwargs,
):
    # 处理图片
    frame = cv2.imread(image_path)

    origin_resolution = (frame.shape[1], frame.shape[0])
    current_areas_to_judge = adjust_areas_to_resolution(areas_to_judge, origin_resolution)
    text_regions = find_text_region(
        frame, easyocr_reader, current_areas_to_judge, origin_resolution
    )

    if remove_type == "gblur":
        sigma = kwargs.get("sigma", 100)
        output_video_path = (
            (Path(output_path) / f"{Path(image_path).name}")
            if original_filename
            else (Path(output_path) / f"{Path(image_path).stem}_{remove_type}_sigma-{sigma}.jpg")
        )
        command = build_ffmpeg_command(
            origin_resolution,
            image_path,
            output_video_path,
            text_regions,
            remove_type,
            sigma,
            is_image=True,
        )
        try:
            run_ffmpeg_command(command)
            logger.info(f"{output_video_path}去水印成功")
        except Exception:
            logger.error(f"{output_video_path}去水印失败")
    else:
        raise ValueError(f"Invalid remove_type {remove_type}. Use 'gblur' or 'boxblur'.")


def remove_watermark_for_single_video(
    video_path,
    output_path,
    easyocr_reader,
    remove_type="gblur",
    original_filename=False,
    **kwargs,
):
    cap = cv2.VideoCapture(video_path)
    ret, frame = cap.read()
    cap.release()
    origin_resolution = (frame.shape[1], frame.shape[0])
    current_areas_to_judge = adjust_areas_to_resolution(areas_to_judge, origin_resolution)
    text_regions = find_text_region(
        frame, easyocr_reader, current_areas_to_judge, origin_resolution
    )
    # 初始化 FFmpeg 输入
    type_config = {**kwargs}

    if remove_type == "gblur":
        # 对指定区域裁剪并应用高斯模糊
        sigma = type_config.get("sigma", 100)

        output_video_path = (
            (Path(output_path) / f"{Path(video_path).name}")
            if original_filename
            else (Path(output_path) / f"{Path(video_path).stem}_{remove_type}_sigma-{sigma}.mp4")
        )
        command = build_ffmpeg_command(
            origin_resolution,
            video_path,
            output_video_path,
            text_regions,
            remove_type,
            sigma,
            is_image=False,
        )
        try:
            run_ffmpeg_command(command)
            logger.info(f"{output_video_path}去水印成功")
        except Exception:
            logger.error(f"{output_video_path}去水印失败")
    else:
        raise ValueError(f"Invalid remove_type {remove_type}. Use 'gblur' or 'boxblur'.")


def remove_watermark_for_single_media(
    media_path,
    output_path,
    easyocr_reader,
    remove_type="gblur",
    original_filename=False,
    skip_if_exist=True,
    **kwargs,
):
    media_path = Path(media_path)
    # 解析 opts 参数
    if kwargs.get("opts"):
        for opt in kwargs["opts"]:
            if "=" in opt:
                key, value = opt.split("=")
                kwargs[key] = value
    output_abs_path = os.path.join(output_path, Path(media_path).name)
    if skip_if_exist and os.path.exists(output_abs_path):
        logger.info(f"目标文件{output_abs_path}存在，跳过")
        return
    if media_path.suffix == ".mp4":
        remove_watermark_for_single_video(
            media_path,
            output_path,
            easyocr_reader,
            remove_type=remove_type,
            original_filename=original_filename,
            **kwargs,
        )
    elif media_path.suffix == ".jpg":
        remove_watermark_for_single_image(
            media_path,
            output_path,
            easyocr_reader,
            remove_type=remove_type,
            original_filename=original_filename,
            **kwargs,
        )
    else:
        raise ValueError(f"Unsupported media type: {Path(media_path).suffix}")


def find_all_media(input_dir_path):
    video_list = list(Path(input_dir_path).rglob("*.mp4"))
    image_list = list(Path(input_dir_path).rglob("*.jpg"))
    media_list = video_list + image_list
    return media_list


def main(args, reader=None, all_test=False, original_filename=False, skip_if_exist=True):
    output_dir = Path(args.output_path)
    output_dir.mkdir(parents=True, exist_ok=True)
    if all_test:
        sigma_all = [
            41,
            42,
            43,
            44,
            45,
            46,
            47,
            48,
            49,
            50,
            51,
            52,
            53,
            54,
            55,
            56,
            57,
            58,
            59,
            60,
            70,
            80,
            90,
            100,
        ]
        if Path(args.media_path).is_dir():
            media_paths = find_all_media(args.media_path)
            for media_path in tqdm(media_paths, desc="Processing test media...", position=0):
                for sigma in tqdm(
                    sigma_all,
                    desc="Processing sigma...",
                    position=1,
                    leave=False,
                ):
                    remove_watermark_for_single_media(
                        media_path,
                        args.output_path,
                        reader,
                        remove_type="gblur",
                        original_filename=original_filename,
                        skip_if_exist=skip_if_exist,
                        sigma=sigma,
                    )
        else:
            for sigma in tqdm(sigma_all, desc="Processing sigma...", position=0):
                remove_watermark_for_single_media(
                    args.media_path,
                    args.output_path,
                    reader,
                    remove_type="gblur",
                    original_filename=original_filename,
                    skip_if_exist=skip_if_exist,
                    sigma=sigma,
                )

        return
    if Path(args.media_path).is_dir():
        # 如果为目录，保留子目录的关系
        media_paths = find_all_media(args.media_path)
        futures = []
        with ThreadPoolExecutor(max_workers=6) as executor:
            for media_path in media_paths:
                relative_path = media_path.relative_to(args.media_path).parent
                output_dir = os.path.normpath(os.path.join(args.output_path, str(relative_path)))
                Path(output_dir).mkdir(parents=True, exist_ok=True)
                future = executor.submit(
                    remove_watermark_for_single_media,
                    media_path,
                    output_dir,
                    reader,
                    args.remove_type,
                    original_filename,
                    skip_if_exist,
                )
                futures.append(future)

        # 使用 tqdm 显示进度
        for future in tqdm(
            as_completed(futures), total=len(media_paths), desc="Processing media..."
        ):
            try:
                future.result()  # 获取任务结果
            except Exception as e:
                logger.error(f"{media_path} 处理失败: {e}")
    else:
        remove_watermark_for_single_media(
            args.media_path,
            args.output_path,
            reader,
            remove_type=args.remove_type,
            original_filename=original_filename,
            skip_if_exist=skip_if_exist,
            opts=args.opts,
        )


# os.environ["FFMPEG_BINARY"] = (
#     "C:\\bevy\\ffmpeg-master-latest-win64-gpl\\bin\\ffmpeg.exe"
# )

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-i",
        "--media_path",
        type=str,
        required=True,
        help="Path to the video file or directory",
    )
    parser.add_argument(
        "-o",
        "--output_path",
        type=str,
        required=True,
        help="Path to the output video file or directory",
    )
    parser.add_argument(
        "-t",
        "--remove_type",
        type=str,
        default="gblur",
        help="Type of watermark removal",
    )
    parser.add_argument(
        "-v",
        "--test_version",
        action="store_true",
        help="Test all parameters",
    )
    parser.add_argument(
        "-s",
        "--original_filename",
        action="store_true",
        help="Keep original filename",
    )
    parser.add_argument(
        "-skip",
        "--skip_if_exist",
        action="store_true",
        default=True,  # 设置默认值为 True
        help="if target file exist, skip action",
    )
    parser.add_argument(
        "opts",
        help="""
Modify config options at the end of the command. For Yacs configs, use
space-separated "PATH.KEY VALUE" pairs.
For python-based LazyConfig, use "path.key=value".
        """.strip(),
        default=None,
        nargs=argparse.REMAINDER,
    )
    # 初始化EasyOCR
    reader = easyocr.Reader(["ch_sim"])
    args = parser.parse_args()
    main(args, reader, args.test_version, args.original_filename, args.skip_if_exist)
