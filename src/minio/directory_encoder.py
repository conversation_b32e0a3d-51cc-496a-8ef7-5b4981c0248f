import logging
from minio import Minio
import pandas as pd
import os
import time
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# 定义本地缓存目录
CACHE_DIR = "minio_cache"
os.makedirs(CACHE_DIR, exist_ok=True)


class DirectoryEncoder:
    def __init__(self):
        self.class_type_dict = {}  # 考试类型字典
        self.area_dict = {}  # 地区字典
        self.minio_client = None
        self.setup_minio()
        self.load_base_data()

    def setup_minio(self):
        """连接Minio服务器"""
        try:
            self.minio_client = Minio(
                "192.168.35.254:9000",
                access_key="minioadmin",
                secret_key="minioadmin_scyd@lab1234",
                secure=False,
            )
            logging.info("Minio服务器连接成功")
        except Exception as e:
            logging.error(f"Minio连接失败: {str(e)}")
            raise

    def check_file_exists(self, local_path):
        """检查本地缓存文件是否存在且有效"""
        if os.path.exists(local_path):
            # 检查文件是否在24小时内更新过
            file_time = os.path.getmtime(local_path)
            current_time = time.time()
            if current_time - file_time < 24 * 3600:  # 24小时内的文件视为有效
                logging.info(f"使用本地缓存文件: {local_path}")
                return True
            else:
                logging.info(f"缓存文件已过期: {local_path}")
        return False

    def download_file(self, bucket_name, object_name, local_path):
        """从Minio下载文件，如果本地已有有效缓存则直接使用"""
        try:
            if self.check_file_exists(local_path):
                return True

            self.minio_client.fget_object(bucket_name, object_name, local_path)
            logging.info(f"成功下载文件: {object_name}")
            return True
        except Exception as e:
            logging.error(f"下载文件 {object_name} 失败: {str(e)}")
            return False

    def load_base_data(self):
        """加载基础数据文件"""
        # 使用缓存目录而不是临时目录
        # 下载并加载考试类型文件
        class_type_path = os.path.join(CACHE_DIR, "class_type.txt")
        if self.download_file("edu-xunkao-base", "class_type.txt", class_type_path):
            try:
                with open(class_type_path, "r", encoding="utf-8") as f:
                    for line in f:
                        parts = line.strip().split("\t")  # 使用tab作为分隔符
                        if len(parts) == 2:
                            self.class_type_dict[parts[0]] = parts[1]
                logging.info("考试类型数据加载成功")
            except Exception as e:
                logging.error(f"解析考试类型文件失败: {str(e)}")

        # 下载并加载地区标准文件
        area_path = os.path.join(CACHE_DIR, "cn_area_standard-0122.csv")
        if self.download_file("edu-xunkao-base", "cn_area_standard-0122.csv", area_path):
            try:
                df = pd.read_csv(area_path)
                for _, row in df.iterrows():
                    area_key = f"{row.iloc[-1]}"  # 最后一列为地址
                    self.area_dict[area_key] = str(int(row.iloc[0]) + 10)  # 索引值加10
                logging.info("地区数据加载成功")
            except Exception as e:
                logging.error(f"解析地区标准文件失败: {str(e)}")

    def encode_date(self, date_str):
        """将日期编码为16进制
        直接将日期字符串(YYYYMMDD)转为整数后转16进制
        例如: 20241231 -> 0x134EE37
        """
        try:
            # 直接将日期字符串转为整数
            date_int = int(date_str)
            # 转换为16进制并移除'0x'前缀，转大写
            return hex(date_int)[2:].upper()
        except Exception as e:
            logging.error(f"日期编码失败 {date_str}: {str(e)}")
            return None

    def encode_area(self, province, city):
        """编码地区信息
        分别获取省份和城市的编码，用点号连接
        例如: 江西,南昌 -> 36.361
        """
        try:
            province_code = None
            city_code = None

            # 1. 获取省份编码
            if province in self.area_dict:
                province_code = self.area_dict[province]
            else:
                logging.error(f"未找到省份编码: {province}")
                return None

            # 2. 获取城市编码
            full_address = f"{province},{city}"
            if full_address in self.area_dict:
                city_code = self.area_dict[full_address]
            else:
                logging.warning(f"未找到城市编码 {full_address}，将只使用省份编码")
                city_code = "000"

            # 3. 拼接编码结果
            return f"{province_code}.{city_code}"

        except Exception as e:
            logging.error(f"地区编码失败 {province}-{city}: {str(e)}")
            return None

    def encode_directory(self, directory):
        """编码目录名称"""
        try:
            # 解析目录各部分
            parts = directory.split("-")
            if len(parts) != 4:
                logging.error(f"目录格式错误: {directory}")
                return None

            exam_type, date_str, province, city = parts

            # 1. 编码考试类型
            type_code = self.class_type_dict.get(exam_type)
            if not type_code:
                logging.error(f"未找到考试类型编码: {exam_type}")
                return None

            # 2. 编码日期
            date_code = self.encode_date(date_str)
            if not date_code:
                return None

            # 3. 编码地区
            area_code = self.encode_area(province, city)
            if not area_code:
                return None

            # 拼接编码结果
            encoded = f"valid.{type_code}.{date_code}.{area_code}"
            logging.info(f"目录 {directory} 编码成功: {encoded}")
            return encoded

        except Exception as e:
            logging.error(f"目录编码失败 {directory}: {str(e)}")
            return None


def encode_single_directory(directory):
    """
    编码单个目录
    Args:
        directory: 原始目录名，格式如：期末考试-20241231-江西-南昌
    Returns:
        编码后的目录名，如：valid.1.134EE37.36.361
    """
    encoder = DirectoryEncoder()
    encoded = encoder.encode_directory(directory)
    if encoded:
        return encoded
    return None


def main():
    if len(sys.argv) > 1:
        # 如果提供了命令行参数，处理单个目录
        directory = sys.argv[1]
        encoded = encode_single_directory(directory)
        if encoded:
            print(f"原始目录: {directory}")
            print(f"编码结果: {encoded}")
    else:
        # 否则运行测试用例
        encoder = DirectoryEncoder()
        test_directories = [
            "期末考试-20241231-江西-南昌",
            "学业水平考试-20241227-山东-滨州",
            "期末考试-20250106-四川-成都",
            "研究生考试-20241221-四川-全域"
        ]

        for directory in test_directories:
            encoded = encoder.encode_directory(directory)
            if encoded:
                print(f"原始目录: {directory}")
                print(f"编码结果: {encoded}")
                print("-" * 50)


if __name__ == "__main__":
    main()
