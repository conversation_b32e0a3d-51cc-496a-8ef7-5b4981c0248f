import logging
from minio import Minio
import pandas as pd
from collections import defaultdict
import time
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# 创建输出目录
OUTPUT_DIR = "minio_inventory_results"
os.makedirs(OUTPUT_DIR, exist_ok=True)

def connect_minio():
    """连接Minio服务器"""
    logging.info("正在连接Minio服务器...")
    minio_client = Minio(
        "36.133.207.229:9000",
        access_key="oss",
        secret_key="NhRBqL9tuV5Z/k*P",
        secure=False,
    )
    logging.info("Minio服务器连接成功")
    return minio_client

def process_directory(minio_client, bucket_name, prefix=""):
    """处理指定前缀的目录，只保留完整路径的统计"""
    start_time = time.time()
    logging.info(f"开始处理目录: {prefix}")
    
    dir_structure = defaultdict(int)  # 只存储完整路径的计数
    processed_count = 0
    last_progress_time = time.time()
    max_levels = 0  # 记录最大层级数

    try:
        objects = minio_client.list_objects(bucket_name, recursive=True, prefix=prefix)
        
        for obj in objects:
            processed_count += 1
            path_parts = obj.object_name.split("/")
            
            # 只统计完整路径（文件所在的最终目录）
            if len(path_parts) > 1:  # 确保不是根目录
                dir_key = "/".join(path_parts[:-1])  # 去掉文件名部分
                dir_structure[dir_key] += 1
                
                # 更新最大层级数
                current_levels = len(path_parts) - 1  # 减去文件名
                if current_levels > max_levels:
                    max_levels = current_levels

            # 每5秒记录一次进度
            current_time = time.time()
            if current_time - last_progress_time >= 5:
                logging.info(
                    f"[{prefix}] 已处理 {processed_count} 个对象，耗时: {current_time - start_time:.2f} 秒"
                )
                last_progress_time = current_time

    except Exception as e:
        logging.error(f"处理目录 {prefix} 时发生错误: {str(e)}")
        raise

    logging.info(
        f"目录 {prefix} 处理完成，共处理 {processed_count} 个对象，最大层级: {max_levels}，耗时: {time.time() - start_time:.2f} 秒"
    )
    return dir_structure, max_levels


def convert_to_dataframe(dir_structure, max_levels):
    """将目录结构转换为DataFrame，只包含完整路径"""
    results = []
    
    for path, count in dir_structure.items():
        levels = path.split("/")
        
        # 创建行数据，动态适应层级
        row = {"文件数量": count}
        for i in range(max_levels):
            level_name = f"{i+1}级目录"
            if i < len(levels):
                row[level_name] = levels[i]
            else:
                row[level_name] = ""  # 对于不存在的层级留空
        
        results.append(row)
    
    # 按层级排序
    sort_columns = [f"{i+1}级目录" for i in range(max_levels)]
    df = pd.DataFrame(results).sort_values(by=sort_columns)
    
    return df


def save_results(df, filename):
    """保存结果到CSV文件"""
    filepath = os.path.join(OUTPUT_DIR, filename)
    logging.info(f"正在保存清单到 {filepath}...")
    df.to_csv(filepath, index=False, encoding="utf-8-sig")
    logging.info(f"清单已成功保存到 {filepath}")

def get_directory_inventory(minio_client, bucket_name):
    """获取目录清单，处理不同类型的目录"""
    start_time = time.time()
    logging.info(f"开始统计 {bucket_name} 存储桶的目录资产清单...")
    
    # 定义要处理的目录前缀
    prefixes = [
        "videos/alert_video_clips",
        "images/alert_images",
        "images/sampled_images"
    ]
    
    all_dfs = []
    max_levels = 0
    
    for prefix in prefixes:
        logging.info(f"开始处理 {prefix} 目录...")
        dir_structure, current_max_levels = process_directory(minio_client, bucket_name, prefix)
        
        # 更新最大层级数
        if current_max_levels > max_levels:
            max_levels = current_max_levels
        
        # 转换为DataFrame并保存单独结果
        df = convert_to_dataframe(dir_structure, max_levels)
        all_dfs.append(df)
        
        # 生成文件名
        filename = prefix.replace("/", "_") + "_inventory.csv"
        save_results(df, filename)
    
    # 合并所有结果
    final_df = pd.concat(all_dfs, ignore_index=True)
    
    # 按所有层级排序
    sort_columns = [f"{i+1}级目录" for i in range(max_levels)]
    final_df = final_df.sort_values(by=sort_columns)
    
    end_time = time.time()
    logging.info(f"目录资产清单统计完成，最大层级: {max_levels}，总耗时: {end_time - start_time:.2f} 秒")
    return final_df


if __name__ == "__main__":
    # 连接Minio
    minio_client = connect_minio()
    bucket_name = "xk-video"

    dir_structure, max_levels = process_directory(minio_client=minio_client, bucket_name=bucket_name)
    df = convert_to_dataframe(dir_structure, max_levels)
    
    # 获取目录清单
    # inventory_df = get_directory_inventory(minio_client, bucket_name)

    # 保存最终结果到CSV文件
    save_results(df, f"{bucket_name}_final.csv")