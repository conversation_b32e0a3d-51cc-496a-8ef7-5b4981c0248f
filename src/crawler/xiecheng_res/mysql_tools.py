# -*- coding: utf-8 -*-
import pymysql
from pymysql.err import Error, DatabaseError, InterfaceError


class MySQLDBTool:
    def __init__(self, host='**************', port=3306, username='root', password='123456', database='wenlv'):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.database = database
        self.connection = None
        self.cursor = None
        self._connect()

    def _connect(self):
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.username,
                password=self.password,
                database=self.database,
                autocommit=True,  # 开启自动提交
                charset='utf8mb4',  # 设置字符集
                cursorclass=pymysql.cursors.Cursor  # 游标类型
            )
            if self.connection.open:
                self.cursor = self.connection.cursor()
                print("MySQL 连接成功")
        except Error as e:
            print(f"MySQL 连接失败: {e}")

    def _execute_query(self, query, params=None):
        try:
            # 检查连接并重新连接
            self.connection.ping(reconnect=True)
            # 每次使用前关闭旧游标并创建新游标
            if self.cursor:
                self.cursor.close()
            self.cursor = self.connection.cursor()
            self.cursor.execute(query, params or ())
            return True
        except (DatabaseError, InterfaceError, Error) as e:
            print(f"数据库操作失败: {e}")
            return False

    def insert_one(self, table_name, data):
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['%s'] * len(data))
        query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"

        if not self._execute_query(query, tuple(data.values())):
            # 检查是否是表不存在的错误（错误码 1146）
            if self._execute_query(f"SHOW TABLES LIKE '{table_name}'") and self.cursor.fetchone() is None:
                # 表不存在，创建新表
                create_table_query = self._generate_create_table_query(table_name, data)
                if self._execute_query(create_table_query):
                    print(f"表 {table_name} 已创建")
                    # 再次尝试插入数据
                    if self._execute_query(query, tuple(data.values())):
                        return self.cursor.lastrowid
        else:
            return self.cursor.lastrowid
        return None

    def _generate_create_table_query(self, table_name, data):
        column_definitions = []
        for column in data.keys():
            column_type = 'LONGTEXT'
            column_definitions.append(f"{column} {column_type}")
        columns_str = ', '.join(column_definitions)
        return f"CREATE TABLE {table_name} (id INT AUTO_INCREMENT PRIMARY KEY, {columns_str})"

    def find_one(self, table_name, where):
        conditions = ' AND '.join([f"{k}=%s" for k in where.keys()])
        query = f"SELECT * FROM {table_name} WHERE {conditions} LIMIT 1"

        if self._execute_query(query, tuple(where.values())):
            return self.cursor.fetchone()
        return None

    def find_all(self, table_name, where=None):
        query = f"SELECT * FROM {table_name}"
        params = ()

        if where:
            conditions = ' AND '.join([f"{k}=%s" for k in where.keys()])
            query += f" WHERE {conditions}"
            params = tuple(where.values())

        if self._execute_query(query, params):
            return self.cursor.fetchall()
        return None

    def update_one(self, table_name, where, update_data):
        set_clause = ', '.join([f"{k}=%s" for k in update_data.keys()])
        conditions = ' AND '.join([f"{k}=%s" for k in where.keys()])

        query = f"UPDATE {table_name} SET {set_clause} WHERE {conditions} LIMIT 1"
        params = tuple(list(update_data.values()) + list(where.values()))

        if self._execute_query(query, params):
            return self.cursor.rowcount
        return 0

    def delete_one(self, table_name, where):
        conditions = ' AND '.join([f"{k}=%s" for k in where.keys()])
        query = f"DELETE FROM {table_name} WHERE {conditions} LIMIT 1"

        if self._execute_query(query, tuple(where.values())):
            return self.cursor.rowcount
        return 0


# 示例使用方法
if __name__ == "__main__":
    # 初始化 MySQL 工具类
    mysql_tool = MySQLDBTool(
        username='root',
        password='123456',
        database='wenlv'
    )

    # 插入数据
    data = {
        "overview": "故宫博物院介绍",
        "summary": "这里是一个总结",
        "tickets": "门票信息",
        "travel_season": "最佳旅游时节",
        "transport_guide": "交通指南",
        "tips": "小贴士"
    }
    inserted_id = mysql_tool.insert_one('travel_data', data)
    print(f"插入的数据ID: {inserted_id}")

    # 查询数据
    result = mysql_tool.find_one('travel_data', {"overview": "故宫博物院介绍"})
    print(f"查询到的数据: {result}")

    # 更新数据
    update_result = mysql_tool.update_one('travel_data',
                                          {"overview": "故宫博物院介绍"},
                                          {"summary": "更新后的总结"}
                                          )
    print(f"更新的数据数量: {update_result}")

    # 删除数据
    """delete_result = mysql_tool.delete_one('travel_data', {"overview": "故宫博物院介绍"})
    print(f"删除的数据数量: {delete_result}")"""