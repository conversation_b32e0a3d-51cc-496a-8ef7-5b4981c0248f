import os
from typing import Dict, Optional, List
from DrissionPage import ChromiumPage, WebPage, ChromiumOptions
from crawler_xiecheng import XiechengCrawler, log_execution
from logger import LoggerManager
import uuid
import time
from mysql_tools import MySQLDBTool

# 获取logger实例
logger = LoggerManager.setup_logger('XiechengServerCrawler', 'xiecheng_crawler.log')


class XiechengServerCrawler(XiechengCrawler):
    """携程旅游景点爬虫服务器版本

    继承自XiechengCrawler，增加服务器环境支持和单链接测试功能
    """

    @log_execution
    def __init__(self, base_url: str, chrome_path: Optional[str] = None, is_server_mode: bool = True):
        """初始化爬虫实例

        Args:
            base_url: 携程旅游网站基础URL
            chrome_path: Chrome可执行文件路径，服务器环境需指定
        """
        # 不直接调用父类的__init__，因为需要自定义browser初始化
        self.base_url = base_url
        self.is_server_mode = is_server_mode

        # 等待时间配置 - 服务器版本增加等待时间
        self.WAIT_TIME = {
            'SHORT': 0.5,  # 增加短等待时间
            'MEDIUM': 3,  # 增加中等等待时间
            'LONG': 8,  # 增加长等待时间
            'EXTRA_LONG': 15
        }

        # MinIO配置
        self.MINIO_CONFIG = {
            'endpoint': '192.168.34.49:9000',
            'access_key': 'minioadmin',
            'secret_key': 'minioadmin_scyd@lab1234',
            'secure': False
        }

        # 调整为相对路径，方便服务器部署
        self.save_path = os.path.join(os.path.dirname(__file__), "data", "xiecheng")
        os.makedirs(self.save_path, exist_ok=True)
        self.downloader = None

        # 初始化浏览器配置
        self._init_browser(chrome_path)

    @log_execution
    def _init_browser(self, chrome_path: Optional[str] = None) -> None:
        """初始化浏览器配置

        Args:
            chrome_path: Chrome可执行文件路径
        """
        try:

         # 创建浏览器配置对象
            logger.warning('初始化浏览器')
            co = ChromiumOptions()

            # 根据模式设置不同的浏览器配置
            if self.is_server_mode:
                # 服务器环境优化配置
                co.headless(True)  # 启用无头模式
                co.set_argument('--incognito')  # 隐身模式
                co.set_argument('--no-sandbox')  # 禁用沙盒模式
                co.set_argument('--disable-dev-shm-usage')
                co.set_argument('--disable-gpu')
            else:
                # 本地模式，可以看到浏览过程
                co.headless(False)  # 禁用无头模式
                co.set_argument('--incognito')  # 隐身模式，减少缓存影响
                # 添加调试选项，方便本地开发调试
                # co.set_argument('--auto-open-devtools-for-tabs')  # 自动打开开发者工具

            # 通用设置
            co.set_argument('--disable-extensions')  # 禁用扩展
            co.set_argument('--disable-infobars')  # 禁用信息栏

            # 如果指定了Chrome路径，则设置
            if chrome_path:
                user_data_path ='./data_cache_insight'
                co.set_paths(browser_path=chrome_path,user_data_path=user_data_path)

            # 初始化浏览器实例
            self.browser = ChromiumPage(co)
            self.page = self.browser  # 使用同一个浏览器实例
        except:
            logger.error('初始化浏览器失败')

    @log_execution
    def _get_surrounding_spots(self) -> List[Dict]:
        """获取周边景点信息 - 服务器版本优化

        Returns:
            周边景点列表，包含景点名称、URL、距离和评分
        """
        surround_spot = []
        try:
            # 增加等待时间，确保页面加载完成
            time.sleep(self.WAIT_TIME['MEDIUM'])

            # 尝试获取周边景点模块
            try:
                around_div = self.browser.ele(".nearbyModule normalModule", timeout=15)
                time.sleep(self.WAIT_TIME['SHORT'])
                around_list = around_div.eles(".nearbyList")
            except Exception:
                logger.warning('未找到周边景点模块，返回空列表')
                return []

            # 处理第一部分景点（带链接）
            try:
                spots_list = around_list[0].eles(".moduleitem")
                for spot_element in spots_list[:5]:
                    spot_info = self._extract_spot_info(spot_element, with_url=True)
                    if spot_info:
                        surround_spot.append(spot_info)
            except Exception as e:
                logger.warning(f'处理第一部分景点失败: {str(e)}')

            # 处理第二部分景点（不带链接）
            try:
                if len(around_list) > 1:
                    spot_list = around_list[1].eles(".moduleitem")
                    for s_element in spot_list:
                        spot_info = self._extract_spot_info(s_element, with_url=False)
                        if spot_info:
                            surround_spot.append(spot_info)
            except Exception as e:
                logger.warning(f'处理第二部分景点失败: {str(e)}')

            return surround_spot

        except Exception as e:
            logger.error(f'获取周边景点失败: {str(e)}')
            return []

    # @log_execution
    def _extract_spot_info(self, element, with_url: bool = True) -> Dict:
        """提取单个景点信息

        Args:
            element: 景点元素
            with_url: 是否需要获取URL

        Returns:
            景点信息字典
        """
        try:
            spot_name = element.ele(".contentTitle", timeout=5).text
            spot_info = {'name': spot_name, 'url': '', 'distance': '', 'score': ''}

            # 获取URL（仅适用于第一部分景点）
            if with_url:
                try:
                    element.click()
                    time.sleep(self.WAIT_TIME['MEDIUM'])
                    spot_tabs = self.browser.get_tabs()
                    spot_info['url'] = spot_tabs[0].url
                    spot_tabs[0].close()
                except Exception:
                    pass

            # 获取评分
            try:
                spot_info['score'] = element.ele(".commentScore", timeout=5).ele("tag:b").text
            except Exception:
                pass

            # 获取距离
            try:
                spot_info['distance'] = element.ele(".distanceDes", timeout=5).text
            except Exception:
                pass

            return spot_info
        except Exception:
            return None

    @log_execution
    def _get_user_qa(self) -> List[Dict]:
        """获取用户问答信息 - 服务器版本优化

        Returns:
            用户问答列表
        """
        User_QA = []
        qa_page = None

        try:
            # 增加等待时间，确保页面加载完成
            time.sleep(self.WAIT_TIME['MEDIUM'])

            # 尝试进入问答页面
            try:
                QA = self.browser.ele(".askModuleRef", timeout=15).ele(".moreUrl")
                time.sleep(self.WAIT_TIME['SHORT'])
                QA.click()
                time.sleep(self.WAIT_TIME['LONG'])  # 增加等待时间
                tabs = self.browser.get_tabs()
                qa_page = tabs[0]
            except Exception:
                logger.warning('未找到问答模块或无法进入问答页面')
                return []

            # 获取问答列表
            try:
                QA_list = qa_page.ele(".asktag_conlist", timeout=10).ele(".asklist").eles(".cf")
                qa_list = [q.attr('href') for q in QA_list]
            except Exception:
                logger.warning('获取问答列表失败')
                return []

            # 处理每个问答
            for qa in qa_list[:5]:
                try:
                    qa_page.get(qa)
                    time.sleep(self.WAIT_TIME['MEDIUM'])
                    qa_info = self._extract_qa_info(qa_page)
                    if qa_info:
                        User_QA.append(qa_info)
                except Exception as e:
                    logger.warning(f'处理问答失败 - URL: {qa}, 错误: {str(e)}')

        except Exception as e:
            logger.error(f'获取用户问答失败: {str(e)}')

        finally:
            if qa_page:
                try:
                    qa_page.close()
                except Exception:
                    pass

            return User_QA

    @log_execution
    def _extract_qa_info(self, page) -> Dict:
        """提取单个问答信息

        Args:
            page: 问答页面

        Returns:
            问答信息字典
        """
        try:
            # 获取问题内容
            Question = page.ele(".detailmain", timeout=10).ele(".ask_title").text
            time.sleep(self.WAIT_TIME['SHORT'])

            # 获取关键词
            try:
                key_word = page.ele(".ask_tagline", timeout=5).eles(".asktag_item")
                keyword_list = [k.text for k in key_word]
            except Exception:
                keyword_list = []

            # 获取回答列表
            Answer_list = []
            try:
                ans = page.ele(".otheranswer_con", timeout=10).eles("tag:li")
                for user_ans in ans:
                    try:
                        answer_text = user_ans.ele(".answer_text", timeout=5).text
                        if answer_text:
                            Answer_list.append(answer_text)
                    except Exception:
                        continue
            except Exception:
                pass

            return {
                'question_id': str(uuid.uuid4()),
                'question': Question,
                'question_keyword': keyword_list,
                'answer': Answer_list,
            }
        except Exception:
            return None

    @log_execution
    def _get_comments(self, spot_name: str, spot_url: str) -> List[Dict]:
        """获取评论信息 - 服务器版本优化

        Args:
            spot_name: 景点名称
            spot_url: 景点URL

        Returns:
            评论信息列表
        """
        try:
            comments = []
            # 增加等待时间，确保页面加载完成
            time.sleep(self.WAIT_TIME['MEDIUM'])

            try:
                comment_elements = self.browser.ele(".commentModuleRef", timeout=15).ele(".commentList").eles(
                    ".commentItem")
            except Exception:
                logger.warning('未找到评论模块')
                return []

            for element in comment_elements[:3]:
                try:
                    uid = str(uuid.uuid4())
                    comment = {
                        'name': spot_name,
                        'scenic_url': spot_url,
                        'user_id': uid,
                        'comment_text': '',
                        'score': '',
                        'img': [],
                        'comment_time': ''
                    }

                    # 获取评论文本
                    try:
                        comment['comment_text'] = element.ele(".contentInfo", timeout=5).ele(".commentDetail").text
                    except Exception:
                        continue

                    # 获取评分
                    try:
                        comment['score'] = element.ele(".contentInfo", timeout=5).ele(".averageScore").text
                    except Exception:
                        pass

                    # 获取评论时间
                    try:
                        comment['comment_time'] = element.ele(".commentFooter", timeout=5).ele(".commentTime").text
                    except Exception:
                        pass

                    # 获取评论图片
                    try:
                        img_elements = element.ele(".commentImgList", timeout=5).eles("tag:a")
                        img_suffix = 0
                        for img in img_elements[:2]:
                            img_url = img.attr('href')
                            if img_url:
                                stored_url = self.download_and_upload_image(
                                    img_url,
                                    f"{spot_name}_comment_{uid}_{img_suffix}"
                                )
                                if stored_url:
                                    comment['img'].append(stored_url)
                            img_suffix += 1
                    except Exception:
                        pass

                    comments.append(comment)
                except Exception as e:
                    logger.warning(f'处理评论失败: {str(e)}')
                    continue

            return comments
        except Exception as e:
            logger.error(f'获取评论信息失败: {str(e)}')
            return []

    @log_execution
    def crawl_single_spot(self, spot_url: str) -> Dict:
        """采集单个景点信息

        Args:
            spot_url: 景点详情页URL

        Returns:
            景点详细信息字典
        """
        try:
            if self.browser.tabs_count < 1:
                self.browser.quit()
                self._init_browser()
            # 直接调用父类的get_spot_details方法
            spot_data = self.get_spot_details(spot_url)
            if spot_data:
                # 尝试从URL中提取省份和城市信息
                url_parts = spot_url.split('/')
                if len(url_parts) >= 5:
                    spot_data.update({
                        'province_name': url_parts[-3],
                        'city_name': url_parts[-2]
                    })
            return spot_data
        except Exception as e:
            logger.error(f'采集单个景点失败: {str(e)}')
            return {}

    @log_execution
    def batch_crawl_city(self, target_province: str = None, target_city_list: list = None) -> None:
        """批量爬取指定省份或城市的景点信息s\

        Args:
            target_province: 目标省份名称，如果为None则处理所有省份
            target_city: 目标城市名称，如果为None则处理指定省份的所有城市
        """
        # 读取CSV文件
        csv_file = os.path.join(os.path.dirname(__file__), 'city_urls.csv')
        if not os.path.exists(csv_file):
            logger.error(f'CSV文件不存在: {csv_file}')
            return

        # 初始化MySQL连接
        mysql_tool = MySQLDBTool(
            username='root',
            password='123456',
            database='wenlv'
        )

        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            import csv
            reader = csv.DictReader(f)
            for row in reader:
                province = row['省份']
                city = row['城市']

                # 检查是否匹配目标省份或城市
                if target_province and province != target_province:
                    continue
                if target_city_list and city not in target_city_list:
                    continue

                # 解析URL列表
                urls = row['URLs'].strip().split()  # 修改为正确的列名 'URLs'
                if not urls:
                    logger.warning(f'未找到URL: {province} - {city}')
                    continue

                logger.info(f'处理URL列表: {urls}')  # 添加URL列表日志

                # 获取城市景点列表URL（第二个URL）
                if len(urls) >= 2:
                    city_url = urls[0]  # 使用第二个URL（景点列表页）
                    logger.info(f'开始处理: {province} - {city} - {city_url}')

                    # 获取城市下所有景点URL
                    logger.info(f'正在获取城市 {city} 的景点URL列表...')
                    spot_urls = self._get_spot_urls(city_url)
                    logger.info(f'找到 {len(spot_urls)} 个景点')

                    # 处理每个景点
                    for i, spot_url in enumerate(spot_urls, 1):
                        logger.info(f'正在处理第 {i}/{len(spot_urls)} 个景点: {spot_url}')

                        # 爬取景点详情
                        spot_data = self.crawl_single_spot(spot_url)
                        if spot_data:
                            # 添加省份和城市信息
                            spot_data.update({
                                'province_name': province,
                                'city_name': city
                            })
                            # 保存到数据库
                            self._save_to_database(mysql_tool, spot_data)
                            logger.info(f'成功爬取景点: {spot_data.get("name", "未知")}')
                        else:
                            logger.error(f'景点数据获取失败: {spot_url}')

                        # 清理临时文件
                        self._clean_temp_files()
                    logger.info(f'采集{province}:结束')
                    logger.info(f'休息2分钟')
                    time.sleep(120)
                    # 处理完一个城市后的清理工作
                    self._clean_temp_files()
                else:
                    logger.error(f'URL格式错误 - {province} - {city}: {urls}')

    @log_execution
    def _clean_temp_files(self) -> None:
        """清理临时文件和目录"""
        try:
            # 清理data/xiecheng目录
            temp_dir = os.path.join(os.path.dirname(__file__), "data", "xiecheng")
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir, topdown=False):
                    for name in files:
                        try:
                            os.remove(os.path.join(root, name))
                        except Exception as e:
                            logger.error(f'删除文件失败 {name}: {str(e)}')
                    for name in dirs:
                        try:
                            os.rmdir(os.path.join(root, name))
                        except Exception as e:
                            logger.error(f'删除目录失败 {name}: {str(e)}')
                logger.info(f'已清理临时目录: {temp_dir}')
        except Exception as e:
            logger.error(f'清理临时文件失败: {str(e)}')


def main():
    # 使用示例
    # 1. 服务器环境运行（需指定Chrome路径）
    # 1. 确定是本地模式还是服务器模式
    is_server_mode = False  # false 为本地模式进行调试，true 为服务器模式
    
    # 2. 设置相应的Chrome路径
    chrome_path = None
    if is_server_mode:
        chrome_path = "/usr/bin/google-chrome"  # 使用which chrome查询服务器Chrome位置
    # chrome_path = "/usr/bin/google-chrome"  # 使用which chrome查询


    crawler = XiechengServerCrawler(
        base_url="https://you.ctrip.com/",
        chrome_path=chrome_path,
        is_server_mode=is_server_mode
    )

    try:
        # 2. 测试单个景点采集
        # spot_url = "https://you.ctrip.com/sight/beijing1/229.html"
        # spot_data = crawler.crawl_single_spot(spot_url)
        # print(f"采集结果: {spot_data}")

        mysql_tool = MySQLDBTool(
            username='root',
            password='123456',
            database='wenlv'
        )

        # crawler._save_to_database(mysql_tool, spot_data)
        # logger.info(f'成功爬取景点: {spot_data["name"]}')

        # 3. 测试批量爬取
        # 爬取指定省份的所有城市
        target_province = '江苏'
        # target_city =None
        target_city_list =['徐州']
        crawler.batch_crawl_city(target_province="江苏", target_city_list=target_city_list)

        # 或者爬取指定城市
        # crawler.batch_crawl_city(target_city="杭州")

        # 或者爬取指定省份的指定城市
        # crawler.batch_crawl_city(target_province="浙江", target_city="杭州")

    finally:
        # 4. 清理并关闭浏览器
        crawler._clean_temp_files()
        crawler.browser.quit()


if __name__ == "__main__":
    main()