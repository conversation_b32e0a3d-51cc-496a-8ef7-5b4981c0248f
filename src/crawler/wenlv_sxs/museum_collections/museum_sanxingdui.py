from DrissionPage import ChromiumPage
import time
from Tool.MongoDBTool import MongoDBTool
from Tool.Minio import MinioFileUploader
from Tool.img_download import ImageDownloader


class MuseumCrawler:
    def __init__(self, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')
        origin = '三星堆'
        category_name = ''
        source = ''
        color = ''
        ove = ''
        status = ''
        store_url = ''
        img_json = {
            'img_name': '',
            'img_url': '',
            'img_size': '',
            'width': '',
            'height': '',
            'resolution': '',
        }

        save_path = "F:/MongoDB/museum/"

        t1 = 0.05
        t2 = 5
        # 三星堆文物页面翻页响应时间较长，所以设定5秒避免丢失
        t3 = 1
        t4 = 10
        # 长时间加载避免点击时间无法点击到

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)
        time.sleep(t2)
        # TODO 具体爬取代码

        # 定义正则表达模式，用以匹配href_tags中<img>标签里的src属性值
        pattern = r'<img.*?src="([^"]*)".*?>'

        #定义变量

        try:
            j = 2
            while True:
                # 重新获取 link_elements
                link_elements = browser.ele('.relics-list').eles('.list-card')
                time.sleep(t4)

                # 检查 j 是否达到 12
                if j >= 12:
                    break

                # 获取当前元素
                element = link_elements[j]
                time.sleep(t3)
                j += 1

                img_tag = element.ele('tag:img')
                img = img_tag.attr('src')
                if img == 'https://industry.map.qq.com/cloud/file/2023/07/25/1690257304866_PSbZfP2J.png?imageMogr2/format/webp/ignore-error/1':
                    # 排除异常图片
                    img = ''
                time.sleep(t4)

                # Retry mechanism for clicking
                max_retries = 3
                for retry in range(max_retries):
                    try:
                        element.click()
                        div = browser.ele('.detail-content no-scrollbar')
                        name = div.ele('.detail-content-right').ele('tag:div').text
                        time.sleep(t3)
                        break
                    except Exception as e:
                        print(f"Click failed, retrying ({retry + 1}/{max_retries})")
                        time.sleep(t3)

                div = browser.ele('.detail-content no-scrollbar')
                name = div.ele('.detail-content-right').ele('tag:div').text
                links_div = div.eles('.item-value ff-s')

                era = links_div[0].text
                if era == '-':
                    era = ''

                level = links_div[1].text
                if level == '-':
                    level = ''

                material = links_div[2].text
                if material == '-':
                    material = ''

                dim = links_div[3].text
                if dim == '-':
                    dim = ''

                if img != '':
                    downloader = ImageDownloader(img, save_path, name)
                    img_json = downloader.download()

                    path = save_path + name + '.jpg'
                    uploader = MinioFileUploader(
                        endpoint="192.168.34.46:9000",
                        access_key="minioadmin",
                        secret_key="minioadmin",
                        secure=False
                    )
                    object_name = "一期/museum/collection_img" + name + ".jpg"
                    store_url = uploader.upload_file('wenlv', object_name, path)

                ove = div.ele('.info-desc desc-hide text-truncate').ele('tag:span').text

                data = {
                    'name': name,
                    'category': category_name,
                    'era': era,
                    'collection_level': level,
                    'origin_museum': origin,
                    'source': source,
                    'dimensions': dim,
                    'material': material,
                    'color': color,
                    'overview': ove,
                    'status': status,
                    'img_json': img_json,
                    'oss_url': store_url,
                }

                inserted_id = mongodb_tool.insert_one('collections', data)
                print(f"插入的景点数据ID: {inserted_id}")
                print(f'collection with number {j} complete')

                browser.back()
                time.sleep(t4)

        except Exception as e:
            print(f'Error while crawl collection information as: {e}')

        initial_page = 94  # 设置初始页数，即第一次执行时需要翻页到的页数
        page = 1  # 当前页数

        # 第一次执行时，重复执行翻页到指定page的次数
        while page < initial_page:
            try:
                next_arrow = browser.ele('.pagination-button button-right')
                next_arrow.click()
                time.sleep(t3)
                page += 1
            except Exception as e:
                print(f'Error while navigating to initial page: {e}')
                break

        while True and page < 96:
            try:
                page += 1
                next_arrow = browser.ele('.pagination-button button-right')
                next_arrow.click()
                time.sleep(t2)
                time.sleep(t4)

                j = 0
                while True:
                    # 重新获取 link_elements
                    link_elements = browser.ele('.relics-list').eles('.list-card')
                    time.sleep(t4)

                    # 检查 j 是否达到 12
                    if j >= 1:
                        break

                    # 获取当前元素
                    element = link_elements[j]
                    time.sleep(t3)
                    j += 1

                    img_tag = element.ele('tag:img')
                    img = img_tag.attr('src')
                    if img == 'https://industry.map.qq.com/cloud/file/2023/07/25/1690257304866_PSbZfP2J.png?imageMogr2/format/webp/ignore-error/1':
                        # 排除异常图片
                        img = ''
                    time.sleep(t4)

                    # Retry mechanism for clicking
                    max_retries = 3
                    for retry in range(max_retries):
                        try:
                            element.click()
                            div = browser.ele('.detail-content no-scrollbar')
                            name = div.ele('.detail-content-right').ele('tag:div').text
                            time.sleep(t3)
                            break
                        except Exception as e:
                            print(f"Click failed, retrying ({retry + 1}/{max_retries})")
                            time.sleep(t3)

                    div = browser.ele('.detail-content no-scrollbar')
                    name = div.ele('.detail-content-right').ele('tag:div').text
                    links_div = div.eles('.item-value ff-s')

                    era = links_div[0].text
                    if era == '-':
                        era = ''

                    level = links_div[1].text
                    if level == '-':
                        level = ''

                    material = links_div[2].text
                    if material == '-':
                        material = ''

                    dim = links_div[3].text
                    if dim == '-':
                        dim = ''

                    if img != '':
                        downloader = ImageDownloader(img, save_path, name)
                        img_json = downloader.download()

                        path = save_path + name + '.jpg'
                        uploader = MinioFileUploader(
                            endpoint="192.168.34.46:9000",
                            access_key="minioadmin",
                            secret_key="minioadmin",
                            secure=False
                        )
                        object_name = "一期/museum/collection_img" + name + ".jpg"
                        store_url = uploader.upload_file('wenlv', object_name, path)

                    ove = div.ele('.info-desc desc-hide text-truncate').ele('tag:span').text

                    data = {
                        'name': name,
                        'category': category_name,
                        'era': era,
                        'collection_level': level,
                        'origin_museum': origin,
                        'source': source,
                        'dimensions': dim,
                        'material': material,
                        'color': color,
                        'overview': ove,
                        'status': status,
                        'img_json': img_json,
                        'oss_url': store_url,
                    }

                    inserted_id = mongodb_tool.insert_one('collections', data)
                    print(f"插入的景点数据ID: {inserted_id}")
                    print(f'collection with number {j} complete')

                    browser.back()
                    time.sleep(t4)
                print(f'page {page} complete')
            except Exception as e:
                print(f'Error while crawl collection information as: {e}')
                break


if __name__ == "__main__":
    museum_crawl = MuseumCrawler('https://www.sxd.cn/relics')
    #三星堆
    museum_crawl.crawl()