from DrissionPage import ChromiumPage
from DrissionPage import WebPage
import time
import re
from Tool.MongoDBTool import MongoDBTool
from Tool.Minio import MinioFileUploader
from Tool.img_download import ImageDownloader


class MuseumCrawler:
    def __init__(self, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.page = WebPage('s')

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')
        origin = '故宫博物院'
        category_name = '建筑'
        site = ''
        dim = ''
        era = ''
        level = ''
        status = ''
        store_url = ''
        material = ''
        source = ''
        color = ''
        name = ''
        n = 0
        # 用来计数第几个藏品

        img_json = {
            'img_name': '',
            'img_url': '',
            'img_size': '',
            'width': '',
            'height': '',
            'resolution': '',
        }

        save_path = "F:/MongoDB/museum/"

        pattern = r"。[^。]*(TAG标签耗时：\d+\.\d+ 秒)"

        t1 = 0.05
        t2 = 2
        # 三星堆文物页面翻页响应时间较长，所以设定5秒避免丢失
        t3 = 5
        t4 = 10
        # 长加载时间用以保证网页图片正常显示

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)
        time.sleep(t4)

        #try:
        cate = browser.ele('.box').eles('tag:div')
        cate_num = len(cate) - 3
        # 减去3是因为这个column最后一个元素不是我们想要爬取的目标(它包含了两个div）
        x = 0
        cate_href = []
        while True and x < cate_num:
            cate_href.append(cate[x].ele('tag:a').attr('href'))
            x += 1

        x = 5
        while x < cate_num:
            browser.get(cate_href[x])
            x += 1

            initial_page = 3  # 设置初始页数，即第一次执行时需要翻页到的页数
            p = 0  # 当前页数

            # 第一次执行时，重复执行翻页到指定page的次数
            while p < initial_page:
                try:
                    col = browser.ele('.next')
                    col.click()
                    time.sleep(t3)
                    p += 1
                except Exception as e:
                    print(f'Error while navigating to initial page: {e}')
                    break

            link_lists = []
            era_list = []
            category_name_list = []
            source_list = []

            next_page = 'The next page is exist while now'
            # next_page != 'No next page'
            # 如果一次性跑到底的话可以使用上面这个作为条件写进while，但是由于页数太多，中间容易浏览器本身报错，所以手动分段。
            while True and next_page != 'No next page':
                p += 1
                try:
                    # 从第几个元素开始爬取（第一个tr tag的元素是列表的名称
                    link_elements = browser.ele('.building2').ele('.table1').eles('tag:tr')
                    time.sleep(t2)

                    j = 1
                    while True and j < len(link_elements):
                        try:
                            url = link_elements[j].ele('tag:a')
                            element = link_elements[j].eles('tag:td')
                            era_list.append(element[1].text)
                            category_name_list.append(element[2].text)
                            href = url.attr('href')
                            link_lists.append(href)
                            j += 1
                        except Exception as e:
                            print(f'Error while crawl this page element {j} as: {e}')

                    # 翻页
                    col = browser.ele('.next')
                    col.click()
                    time.sleep(t2)
                except Exception as e:
                    print(f'Error while crawl the next page for {p} as {e} ')
                    next_page = 'No next page'

            i = 4
            # 初始设置，可以从本页的第几个元素开始爬取，后续i会一直累加直到爬完所有后续页面中的藏品
            while True and i < len(link_lists):
                retry = True
                retry_count = 0
                while retry and retry_count < 3:  # 最多重试3次
                    try:
                        browser.get(link_lists[i])
                        era = era_list[i]
                        category_name = category_name_list[i]
                        # source = source_list[i]
                        i += 1

                        div = browser.ele('.text')
                        name = div.ele('tag:span').text
                        time.sleep(t2)

                        ove = div.ele('.content_edit').ele('tag:p').text
                        ove = re.sub(pattern, "。", ove)
                        ove = ove.lstrip()

                        retry = False  # 如果没有异常，跳出重试循环
                    except Exception as e:
                        if '502' in str(e):
                            print(f'Encountered 502 error, retrying... (attempt {retry_count + 1})')
                            retry_count += 1
                            time.sleep(t2)  # 等待一段时间后重试
                        else:
                            print(f'Error while crawling page: {e}')
                            retry = False  # 如果不是502错误，跳出重试循环

                if retry_count >= 3:
                    print(f'Failed to load page after 3 retries, skipping to next page.')
                    continue  # 如果重试次数达到上限，跳过当前页面

                img_tag = browser.ele('.pic').ele('tag:img')
                img = img_tag.attr('src')
                time.sleep(t1)

                path = save_path + name + '.jpg'
                downloader = ImageDownloader(img, save_path, name)
                try:
                    # 因为该网页的图片在下载中可能会出现一种方法不行，必须用另一种方法下载图片。因此设置一个try，每次图片下载的时候如果第一个失败了，尝试第二个
                    self.page.download(img, save_path, name, 'jpg', show_msg=True)
                    img_json = downloader.get_image_info(path)
                except Exception as e:
                    print(f'Erro {e}, while download img, now while try another method to download')
                    img_json = downloader.download()

                uploader = MinioFileUploader(
                    endpoint="192.168.34.46:9000",
                    access_key="minioadmin",
                    secret_key="minioadmin",
                    secure=False
                )
                object_name = "一期/museum/" + name + ".jpg"
                store_url = uploader.upload_file('wenlv', object_name, path)
                img_list = []
                img_list.append({
                    'store_url_list': store_url,
                    'img_json': img_json,
                })

                try:
                    img_tag_list = browser.ele('.child').eles('tag:img')
                    k = 1
                    while k < len(img_tag_list):
                        img = img_tag_list[k].attr('src')
                        time.sleep(t1)
                        n = name + str(k)
                        k += 1

                        path = save_path + name + '.jpg'
                        downloader = ImageDownloader(img, save_path, name)
                        try:
                            self.page.download(img, save_path, name, 'jpg', show_msg=True)
                            img_json = downloader.get_image_info(path)
                        except Exception as e:
                            print(f'Erro {e}, while download img, now while try another method to download')
                            img_json = downloader.download()

                        uploader = MinioFileUploader(
                            endpoint="192.168.34.46:9000",
                            access_key="minioadmin",
                            secret_key="minioadmin",
                            secure=False
                        )
                        object_name = "一期/museum/" + n + ".jpg"
                        store_url = uploader.upload_file('wenlv', object_name, path)
                        img_list.append({
                            'store_url_list': store_url,
                            'img_json': img_json,
                        })
                except Exception as e:
                    print(f"Error occur while crawling as: {e}")

                data = {
                    'name': name,
                    'category': category_name,
                    'era': era,
                    'collection_level': level,
                    'origin_museum': origin,
                    'source': source,
                    'dimensions': dim,
                    'material': material,
                    'color': color,
                    'overview': ove,
                    'status': status,
                    'oss_url': img_list,
                }

                inserted_id = mongodb_tool.insert_one('collections', data)
                print(f"插入的景点数据ID: {inserted_id}")
                n = initial_page * 12 + i
                print(f'collection with number {n} complete')
                time.sleep(t2)



if __name__ == "__main__":
    museum_crawl = MuseumCrawler('https://www.dpm.org.cn/explore/collections.html')
    museum_crawl.crawl()