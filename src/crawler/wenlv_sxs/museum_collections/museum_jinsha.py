from DrissionPage import ChromiumPage
from DrissionPage import WebPage
import time
from Tool.MongoDBTool import MongoDBTool
from Tool.Minio import MinioFileUploader
from Tool.img_download import ImageDownloader


class MuseumCrawler:
    def __init__(self, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.page = WebPage('s')

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')
        origin = '金沙遗址'
        category_name = ''
        source = ''
        color = ''
        era = ''
        level = ''
        status = ''
        store_url = ''
        material = ''

        img_json = {
            'img_name': '',
            'img_url': '',
            'img_size': '',
            'width': '',
            'height': '',
            'resolution': '',
        }

        save_path = "F:/MongoDB/museum/"

        t1 = 0.05
        t2 = 5
        # 三星堆文物页面翻页响应时间较长，所以设定5秒避免丢失
        t3 = 1
        t4 = 10
        # 长加载时间用以保证网页图片正常显示

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)
        time.sleep(t4)
        # TODO 具体爬取代码


        #定义变量

        #try:
        j = 17
        # 从第几个元素开始爬取
        while True:
            # 重新获取 link_elements
            link_elements = browser.ele('.treasures-list container').eles('tag:li')
            time.sleep(t2)

            # 因为是先爬取一页，再选择种类，所以设定if避免报错后重新跑时重复爬取第一页
            if j >= 17:
                # 第一页有17个藏品
                break

            col = browser.ele('.innerNav').eles('tag:a')
            category_name = col[0].text
            material = category_name.replace('器', "")

            # 获取当前元素
            element = link_elements[j]
            ele = element.ele('tag:img')
            time.sleep(t3)
            j += 1

            img_tag = element.ele('tag:img')
            img = img_tag.attr('src')


            ele.click()
            time.sleep(t4)
            div = browser.ele('.treasures-item-content')
            name = div.ele('.col-lg-5').ele('tag:h4').text
            time.sleep(t2)

            links_div = div.ele('.col-lg-5').eles('tag:p')
            dim = links_div[1].text
            ove = links_div[2].text

            res = self.page.download(img, save_path, name, 'png', show_msg=True)
            path = save_path + name + '.png'

            downloader = ImageDownloader(img, save_path, name)
            img_json = downloader.get_image_info(path)

            uploader = MinioFileUploader(
                endpoint="192.168.34.46:9000",
                access_key="minioadmin",
                secret_key="minioadmin",
                secure=False
            )
            object_name = "一期/museum/collection_jinsha" + name + ".jpg"
            store_url = uploader.upload_file('wenlv', object_name, path)


            data = {
                'name': name,
                'category': category_name,
                'era': era,
                'collection_level': level,
                'origin_museum': origin,
                'source': source,
                'dimensions': dim,
                'material': material,
                'color': color,
                'overview': ove,
                'status': status,
                'img_json': img_json,
                'oss_url': store_url,
            }

            inserted_id = mongodb_tool.insert_one('collections', data)
            print(f"插入的景点数据ID: {inserted_id}")
            print(f'collection with number {j} complete')

            exit = browser.ele('.treasures-item-bg').ele('tag:span')
            exit.click()
            time.sleep(t4)

        #except Exception as e:
            #print(f'Error while crawl collection information as: {e}')

        initial_page = 4  # 设置初始页数，即第一次执行时需要翻页到的页数
        p = 0  # 当前页数

        # 第一次执行时，重复执行翻页到指定page的次数
        while p < initial_page:
            try:
                col = browser.ele('.innerNav').eles('tag:a')
                col[p].click()
                time.sleep(t3)
                p += 1
            except Exception as e:
                print(f'Error while navigating to initial page: {e}')
                break

        while True and p < 5:
            try:
                j = 0
                p += 1
                col = browser.ele('.innerNav').eles('tag:a')
                category_name = col[p].text
                col[p].click()
                time.sleep(t4)

                while True:

                    # 重新获取 link_elements
                    link_elements = browser.ele('.treasures-list container').eles('tag:li')
                    time.sleep(t2)
                    material = category_name.replace('器', "")

                    # 获取当前元素
                    element = link_elements[j]
                    ele = element.ele('tag:img')
                    time.sleep(t3)
                    j += 1

                    img_tag = element.ele('tag:img')
                    img = img_tag.attr('src')

                    ele.click()
                    time.sleep(t4)
                    div = browser.ele('.treasures-item-content')
                    name = div.ele('.col-lg-5').ele('tag:h4').text
                    time.sleep(t2)

                    links_div = div.ele('.col-lg-5').eles('tag:p')
                    dim = links_div[1].text
                    ove = links_div[2].text

                    res = self.page.download(img, save_path, name, 'png', show_msg=True)
                    path = save_path + name + '.png'

                    downloader = ImageDownloader(img, save_path, name)
                    img_json = downloader.get_image_info(path)

                    uploader = MinioFileUploader(
                        endpoint="192.168.34.46:9000",
                        access_key="minioadmin",
                        secret_key="minioadmin",
                        secure=False
                    )
                    object_name = "一期/museum/collection_jinsha" + name + ".jpg"
                    store_url = uploader.upload_file('wenlv', object_name, path)

                    data = {
                        'name': name,
                        'category': category_name,
                        'era': era,
                        'collection_level': level,
                        'origin_museum': origin,
                        'source': source,
                        'dimensions': dim,
                        'material': material,
                        'color': color,
                        'overview': ove,
                        'status': status,
                        'img_json': img_json,
                        'oss_url': store_url,
                    }

                    inserted_id = mongodb_tool.insert_one('collections', data)
                    print(f"插入的景点数据ID: {inserted_id}")
                    print(f'collection with number {j} complete')

                    exit = browser.ele('.treasures-item-bg').ele('tag:span')
                    exit.click()
                    time.sleep(t4)


            except Exception as e:
                print(f'Error while crawl collection information as: {e}')
                break


if __name__ == "__main__":
    museum_crawl = MuseumCrawler('https://www.jinshasitemuseum.com/node/197')
    # 金沙
    museum_crawl.crawl()