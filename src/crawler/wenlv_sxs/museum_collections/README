Gugong.py: 用来爬取故宫博物院内的建筑文物的信息，字典如下:
                data = {
                    'name': name,  建筑名
                    'category': category_name,  类别
                    'era': era,  年代
                    'collection_level': level,   文物藏品的级别
                    'origin_museum': origin,  来源博物馆
                    'dimensions': dim,  外形信息
                    'overview': ove,  简介
                    'status': status,  保存状态
                    'oss_url': img_list,
                }
                注: 建筑的oss_url中子类字典格式如下:
                {
                'store_url_list': store_url,  图片保存的Minio位置链接
                'img_json': img_json,    图片的信息
                }

Gugong_collections.py: 爬取故宫博物院内的文物藏品，藏品的字典如下:
                data = {
                    'name': name,  藏品名
                    'category': category_name,  文物类别
                    'era': era,  文物年代
                    'collection_level': level,  文物藏品级别
                    'origin_museum': origin,  来源博物馆
                    'source': source,  出土地
                    'dimensions': dim,   外形
                    'material': material,   材料
                    'color': color,   颜色
                    'overview': ove,   简介
                    'status': status,   保存状态
                    'oss_url': img_list,   图片相关信息
                }
                注: 藏品的oss_url中子类字典格式如下:
                {
                'store_url_list': store_url,  图片保存的Minio位置链接
                'img_json': img_json,    图片的信息
                }

museum_CD.py: 成都博物馆的藏品爬虫
museum_jinsha.py: 金沙博物馆藏品爬虫
museum_sanxingdui.py: 三星堆藏品爬虫
shanxi_collections.py: 陕西历史博物馆藏品爬虫

藏品字典和上述基本一致，但是各博物馆提供的藏品信息都有区别，所以可能有数据有，有些数据只能为空。也有些数据可能包含在简介里，需要后期利用大模型进行提取