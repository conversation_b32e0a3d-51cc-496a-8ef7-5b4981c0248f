from DrissionPage import ChromiumPage
from DrissionPage import WebPage
import time
from Tool.MongoDBTool import MongoDBTool
from Tool.Minio import MinioFileUploader
from Tool.img_download import ImageDownloader


class MuseumCrawler:
    def __init__(self, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.page = WebPage('s')

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')
        origin = '陕西历史博物馆'
        category_name = ''
        site = ''
        dim = ''
        era = ''
        level = ''
        status = ''
        store_url = ''
        ove = ''
        material = ''
        source = ''
        color = ''
        name = ''
        n = 0
        # 用来计数第几个藏品

        img_json = {
            'img_name': '',
            'img_url': '',
            'img_size': '',
            'width': '',
            'height': '',
            'resolution': '',
        }

        save_path = "F:/MongoDB/museum/"

        pattern = r"。[^。]*(TAG标签耗时：\d+\.\d+ 秒)"

        t1 = 0.05
        t2 = 2
        # 三星堆文物页面翻页响应时间较长，所以设定5秒避免丢失
        t3 = 5
        t4 = 10
        # 长加载时间用以保证网页图片正常显示

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)
        time.sleep(t4)

        initial_page = 0  # 设置初始页数，即第一次执行时需要翻页到的页数
        p = 0  # 当前页数

        # 第一次执行时，重复执行翻页到指定page的次数
        while p < initial_page:
            try:
                col = browser.ele('.paging-item paging-arrow xfont icon-right')
                col.click()
                time.sleep(t3)
                p += 1
            except Exception as e:
                print(f'Error while navigating to initial page: {e}')
                break

        link_lists = []
        era_list = []
        category_name_list = []
        source_list = []

        next_page = 'The next page is exist while now'
        # next_page != 'No next page'
        # 如果一次性跑到底的话可以使用上面这个作为条件写进while，但是由于页数太多，中间容易浏览器本身报错，所以手动分段。
        while True and next_page != 'No next page':
            p += 1
            try:
                link_elements = browser.ele('.collection3 showd').ele('.list').eles('tag:a')
                # 虽然子类中第一项tag 是 div，但是子类里还包含了tag：div,所以用tag:a定位防止爬取不必要的子类。
                time.sleep(t2)

                j = 0
                # 从第几个元素开始爬取
                while True and j < len(link_elements):
                    try:
                        href = link_elements[j].attr('href')
                        link_lists.append(href)
                        j += 1
                    except Exception as e:
                        print(f'Error while crawl this page element {j} as: {e}')

                # 翻页
                col = browser.ele('.next')
                col.click()
                time.sleep(t2)
            except Exception as e:
                print(f'Error while crawl the next page for {p} as {e} ')
                next_page = 'No next page'

        i = 0
        # 初始设置
        while True and i < len(link_lists):
            retry = True
            retry_count = 0
            while retry and retry_count < 3:  # 最多重试3次
                try:
                    browser.get(link_lists[i])
                    i += 1

                    div = browser.ele('.slick-item')
                    name = div.ele('.t').text
                    time.sleep(t2)

                    status = div.ele('.tag').text
                    time.sleep(t2)

                    element = div.ele('.ul').eles('tag:div')
                    era = element[0].text
                    level = element[1].text
                    dim = element[2].text
                    material =element[3].text
                    time.sleep(t2)


                    retry = False  # 如果没有异常，跳出重试循环
                except Exception as e:
                    if '502' in str(e):
                        print(f'Encountered 502 error, retrying... (attempt {retry_count + 1})')
                        retry_count += 1
                        time.sleep(t2)  # 等待一段时间后重试
                    else:
                        print(f'Error while crawling page: {e}')
                        retry = False  # 如果不是502错误，跳出重试循环

            if retry_count >= 3:
                print(f'Failed to load page after 3 retries, skipping to next page.')
                continue  # 如果重试次数达到上限，跳过当前页面

            img_tag = browser.ele('.slick-item').ele('.pic').ele('tag:img')
            img = img_tag.attr('src')
            time.sleep(t1)

            path = save_path + name + '.jpg'
            downloader = ImageDownloader(img, save_path, name)
            try:
                self.page.download(img, save_path, name, 'jpg', show_msg=True)
                img_json = downloader.get_image_info(path)
            except Exception as e:
                print(f'Erro {e}, while download img, now while try another method to download')
                img_json = downloader.download()

            uploader = MinioFileUploader(
                endpoint="*************:9000",
                access_key="minioadmin",
                secret_key="minioadmin",
                secure=False
            )
            object_name = "一期/museum/" + name + ".jpg"
            store_url = uploader.upload_file('wenlv', object_name, path)
            img_list = []
            img_list.append({
                'store_url_list': store_url,
                'img_json': img_json,
            })

            data = {
                'name': name,
                'category': category_name,
                'era': era,
                'collection_level': level,
                'origin_museum': origin,
                'source': source,
                'dimensions': dim,
                'material': material,
                'color': color,
                'overview': ove,
                'status': status,
                'oss_url': img_list,
            }

            inserted_id = mongodb_tool.insert_one('collections', data)
            print(f"插入的景点数据ID: {inserted_id}")
            n = initial_page * 12 + i
            print(f'collection with number {n} complete')
            time.sleep(t2)



if __name__ == "__main__":
    museum_crawl = MuseumCrawler('https://www.sxhm.com/collection.html')
    museum_crawl.crawl()