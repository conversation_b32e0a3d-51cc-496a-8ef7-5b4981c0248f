from DrissionPage import ChromiumPage
from DrissionPage import WebPage
import time
import re
from Tool.MongoDBTool import MongoDBTool
from Tool.Minio import MinioFileUploader
from Tool.img_download import ImageDownloader


class MuseumCrawler:
    def __init__(self, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.page = WebPage('s')

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')
        origin = '故宫博物院'
        category_name = '建筑'
        site = ''
        dim = ''
        era = ''
        level = ''
        status = ''
        store_url = ''
        material = ''

        img_json = {
            'img_name': '',
            'img_url': '',
            'img_size': '',
            'width': '',
            'height': '',
            'resolution': '',
        }

        save_path = "F:/MongoDB/jianzhu/"

        pattern = r"。[^。]*(TAG标签耗时：\d+\.\d+ 秒)"

        t1 = 0.05
        t2 = 2
        # 三星堆文物页面翻页响应时间较长，所以设定5秒避免丢失
        t3 = 5
        t4 = 10
        # 长加载时间用以保证网页图片正常显示

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)
        time.sleep(t4)

        #try:
        j = 1
        # 从第几个元素开始爬取（第一个tr tag的元素是列表的名称）
        link_lists = []
        link_elements = browser.ele('.table1').eles('tag:tr')
        time.sleep(t2)
        while True and j < 13:
                url = link_elements[j].ele('tag:a')
                href = url.attr('href')
                link_lists.append(href)
                j += 1

        i = 12
        while True and i < 12:
            browser.get(link_lists[i])
            i += 1
            div = browser.ele('.text')
            name = div.ele('tag:span').text
            time.sleep(t2)

            links_div = div.ele('.p').eles('tag:p')
            era = links_div[0].text
            era = era.split('：')[1]

            category_name = links_div[1].text
            category_name = category_name.split('：')[1]

            site = links_div[2].text
            site = site.split('：')[1]

            ove = div.ele('.content_edit').ele('tag:p').text
            ove = re.sub(pattern, "。", ove)

            img_tag = browser.ele('.pic').ele('tag:img')
            img = img_tag.attr('src')
            time.sleep(t1)
            # self.page.download(img, save_path, name, 'jpg', show_msg=True)
            path = save_path + name + '.jpg'

            downloader = ImageDownloader(img, save_path, name)
            img_json = downloader.download()
            # img_json = downloader.get_image_info(path)

            uploader = MinioFileUploader(
                endpoint="192.168.34.46:9000",
                access_key="minioadmin",
                secret_key="minioadmin",
                secure=False
            )
            object_name = "一期/jianzhu/" + name + ".jpg"
            store_url = uploader.upload_file('wenlv', object_name, path)
            img_list = []
            img_list.append({
                'store_url_list': store_url,
                'img_json': img_json,
            })

            img_tag_list = browser.ele('.child').eles('tag:img')
            k = 1
            while k < len(img_tag_list):
                img = img_tag_list[k].attr('src')
                time.sleep(t1)
                n = name + str(k)
                k += 1

                path = save_path + n + '.jpg'

                downloader = ImageDownloader(img, save_path, n)
                img_json = downloader.download()
                # img_json = downloader.get_image_info(path)

                uploader = MinioFileUploader(
                    endpoint="192.168.34.46:9000",
                    access_key="minioadmin",
                    secret_key="minioadmin",
                    secure=False
                )
                object_name = "一期/jianzhu/" + n + ".jpg"
                store_url = uploader.upload_file('wenlv', object_name, path)
                img_list.append({
                    'store_url_list': store_url,
                    'img_json': img_json,
                })

            data = {
                'name': name,
                'category': category_name,
                'era': era,
                'collection_level': level,
                'origin_museum': origin,
                'dimensions': dim,
                'overview': ove,
                'status': status,
                'oss_url': img_list,
            }

            inserted_id = mongodb_tool.insert_one('jianzhu', data)
            print(f"插入的景点数据ID: {inserted_id}")
            print(f'collection with number {j} complete')
            time.sleep(t2)

        #except Exception as e:
            #print(f'Error while crawl collection information as: {e}')

        initial_page = 7  # 设置初始页数，即第一次执行时需要翻页到的页数
        p = 0  # 当前页数

        # 第一次执行时，重复执行翻页到指定page的次数
        while p < initial_page:
            try:
                col = browser.ele('.next')
                col.click()
                time.sleep(t3)
                p += 1
            except Exception as e:
                print(f'Error while navigating to initial page: {e}')
                break

        link_lists = []
        j = 1
        try:
            while True and p < 11:
                p += 1
                col = browser.ele('.next')
                col.click()
                time.sleep(t2)

                # 从第几个元素开始爬取（第一个tr tag的元素是列表的名称
                link_elements = browser.ele('.table1').eles('tag:tr')
                time.sleep(t2)
                while True and j < 13:
                    url = link_elements[j].ele('tag:a')
                    href = url.attr('href')
                    link_lists.append(href)
                    j += 1
                j = 1

            i = 0
            while True and i < len(link_lists):
                browser.get(link_lists[i])
                i += 1
                div = browser.ele('.text')
                name = div.ele('tag:span').text
                time.sleep(t2)

                links_div = div.ele('.p').eles('tag:p')
                era = links_div[0].text
                era = era.split('：')[1]

                category_name = links_div[1].text
                category_name = category_name.split('：')[1]

                site = links_div[2].text
                site = site.split('：')[1]

                ove = div.ele('.content_edit').ele('tag:p').text
                ove = re.sub(pattern, "。", ove)

                img_tag = browser.ele('.pic').ele('tag:img')
                img = img_tag.attr('src')
                time.sleep(t1)
                # self.page.download(img, save_path, name, 'jpg', show_msg=True)
                path = save_path + name + '.jpg'

                downloader = ImageDownloader(img, save_path, name)
                img_json = downloader.download()
                # img_json = downloader.get_image_info(path)

                uploader = MinioFileUploader(
                    endpoint="192.168.34.46:9000",
                    access_key="minioadmin",
                    secret_key="minioadmin",
                    secure=False
                )
                object_name = "一期/jianzhu/" + name + ".jpg"
                store_url = uploader.upload_file('wenlv', object_name, path)
                img_list = []
                img_list.append({
                    'store_url_list': store_url,
                    'img_json': img_json,
                })
                try:
                    img_tag_list = browser.ele('.child').eles('tag:img')
                    k = 1
                    while k < len(img_tag_list):
                        img = img_tag_list[k].attr('src')
                        time.sleep(t1)
                        n = name + str(k)
                        k += 1

                        path = save_path + n + '.jpg'

                        downloader = ImageDownloader(img, save_path, n)
                        img_json = downloader.download()
                        # img_json = downloader.get_image_info(path)

                        uploader = MinioFileUploader(
                            endpoint="192.168.34.46:9000",
                            access_key="minioadmin",
                            secret_key="minioadmin",
                            secure=False
                        )
                        object_name = "一期/jianzhu/" + n + ".jpg"
                        store_url = uploader.upload_file('wenlv', object_name, path)
                        img_list.append({
                            'store_url_list': store_url,
                            'img_json': img_json,
                        })
                except Exception as e:
                    print(f"Error occur while crawling as: {e}")

                data = {
                    'name': name,
                    'category': category_name,
                    'era': era,
                    'collection_level': level,
                    'origin_museum': origin,
                    'dimensions': dim,
                    'overview': ove,
                    'status': status,
                    'oss_url': img_list,
                }

                inserted_id = mongodb_tool.insert_one('jianzhu', data)
                print(f"插入的景点数据ID: {inserted_id}")
                print(f'collection with number {j} complete')
                time.sleep(t2)

        except Exception as e:
            print(f'Error while crawl collection information as: {e}')


if __name__ == "__main__":
    museum_crawl = MuseumCrawler('https://www.dpm.org.cn/explore/buildings.html')
    museum_crawl.crawl()