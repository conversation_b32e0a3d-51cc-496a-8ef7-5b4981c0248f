from DrissionPage import ChromiumPage
from DrissionPage import WebPage

import time
from Tool.MongoDBTool import MongoDBTool
from Tool.Minio import MinioFileUploader
from Tool.img_download import ImageDownloader


class MuseumCrawler:
    def __init__(self, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.page = WebPage('s')

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')
        origin = '四川博物院'
        category_name = ''
        source = ''
        color = ''
        era = ''
        level = ''
        status = ''
        store_url = ''
        material = ''

        img_json = {
            'img_name': '',
            'img_url': '',
            'img_size': '',
            'width': '',
            'height': '',
            'resolution': '',
        }

        save_path = "F:/MongoDB/museum/"

        t1 = 0.05
        t2 = 5
        # 三星堆文物页面翻页响应时间较长，所以设定5秒避免丢失
        t3 = 1
        t4 = 10
        # 长加载时间用以保证网页图片正常显示

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)
        time.sleep(t4)
        # TODO 具体爬取代码


        #定义变量

        #try:
        j = 6
        # 正在爬取第几个类
        while True and j < 7:
            # 重新获取 link_elements
            collection_category = browser.ele(".content-item-list").eles("tag:li")
            time.sleep(t2)

            element = collection_category[j]
            category_name = element.ele('tag:div').text
            element.click()
            time.sleep(t3)
            j += 1
            i = 0
            # 正在爬取当前类别下的第几个藏品
            try:
                while True:
                    collections_list = browser.ele('.content-list').eles('tag:li')
                    collections = collections_list[i]
                    i += 1

                    collections.hover()
                    # 鼠标悬停等待动态画面加载完成
                    time.sleep(t3)
                    collections.click()
                    time.sleep(t3)

                    div = browser.ele('.content')
                    img_tag = div.ele('.content-img').ele('tag:img')
                    img = img_tag.attr('src')

                    name =div.ele('.content-title').text
                    time.sleep(t1)

                    era = div.ele('.content-item-nav-label').ele('tag:span').text
                    time.sleep(t1)

                    d = div.ele('.content-item-nav').eles('tag:div')
                    dim = d[2].text
                    time.sleep(t1)

                    ove = div.ele('.content-dec').ele('tag:iframe').ele('tag:span').text
                    time.sleep(t1)

                    self.page.download(img, save_path, name, 'png', show_msg=True)
                    path = save_path + name + '.png'

                    downloader = ImageDownloader(img, save_path, name)
                    img_json = downloader.get_image_info(path)

                    uploader = MinioFileUploader(
                        endpoint="192.168.34.46:9000",
                        access_key="minioadmin",
                        secret_key="minioadmin",
                        secure=False
                    )
                    object_name = "一期/museum/collection_jinsha" + name + ".jpg"
                    store_url = uploader.upload_file('wenlv', object_name, path)

                    data = {
                        'name': name,
                        'category': category_name,
                        'era': era,
                        'collection_level': level,
                        'origin_museum': origin,
                        'source': source,
                        'dimensions': dim,
                        'material': material,
                        'color': color,
                        'overview': ove,
                        'status': status,
                        'img_json': img_json,
                        'oss_url': store_url,
                    }

                    inserted_id = mongodb_tool.insert_one('collections', data)
                    print(f"插入的景点数据ID: {inserted_id}")
                    print(f'collection with number {j} complete')
                    print(f"collection {name} complete")

                    col = browser.ele('.banner-item').eles('tag:li')
                    back = col[3]
                    back.click()
                    time.sleep(t3)

            except Exception as e:
                print(f'Error while crawl the collection: {e}')

            try:
                p = 1
                # 计数翻页次数
                times = 0
                i = 0
                while True:
                    while times < p:
                        next_list = browser.ele('.hb-pager-inner').eles('tag:a')
                        l = len(next_list) - 1
                        next_arrow = next_list[l]
                        next_arrow.click()
                        times += 1

                    collections_list = browser.ele('.content-list').eles('tag:li')
                    collections = collections_list[i]
                    i += 1

                    collections.hover()
                    # 鼠标悬停等待动态画面加载完成
                    time.sleep(t3)
                    collections.click()
                    time.sleep(t3)

                    div = browser.ele('.content')
                    img_tag = div.ele('.content-img').ele('tag:img')
                    img = img_tag.attr('src')

                    name = div.ele('.content-title').text
                    time.sleep(t1)

                    era = div.ele('.content-item-nav-label').ele('tag:span').text
                    time.sleep(t1)

                    d = div.ele('.content-item-nav').eles('tag:div')
                    dim = d[2].text
                    time.sleep(t1)

                    ove = div.ele('.content-dec').ele('tag:iframe').ele('tag:span').text
                    time.sleep(t1)

                    self.page.download(img, save_path, name, 'png', show_msg=True)
                    path = save_path + name + '.png'

                    downloader = ImageDownloader(img, save_path, name)
                    img_json = downloader.get_image_info(path)

                    uploader = MinioFileUploader(
                        endpoint="192.168.34.46:9000",
                        access_key="minioadmin",
                        secret_key="minioadmin",
                        secure=False
                    )
                    object_name = "一期/museum/collection_jinsha" + name + ".jpg"
                    store_url = uploader.upload_file('wenlv', object_name, path)

                    data = {
                        'name': name,
                        'category': category_name,
                        'era': era,
                        'collection_level': level,
                        'origin_museum': origin,
                        'source': source,
                        'dimensions': dim,
                        'material': material,
                        'color': color,
                        'overview': ove,
                        'status': status,
                        'img_json': img_json,
                        'oss_url': store_url,
                    }

                    inserted_id = mongodb_tool.insert_one('collections', data)
                    print(f"插入的景点数据ID: {inserted_id}")
                    print(f'collection with number {j} complete')
                    print(f"collection {name} complete")

                    if i >= 12:
                        p += 1
                        i = 0

                    col = browser.ele('.banner-item').eles('tag:li')
                    back = col[3]
                    back.click()
                    times = 0
                    time.sleep(t3)


            except Exception as e:
                print(f'Error while crawl the collection: {e}')
                col = browser.ele('.banner-item').eles('tag:li')
                back = col[2]
                back.click()
                pass



if __name__ == "__main__":
    museum_crawl = MuseumCrawler('https://www.scmuseum.cn/sccms/node/290?subnode=267')
    # 四川博物院
    museum_crawl.crawl()