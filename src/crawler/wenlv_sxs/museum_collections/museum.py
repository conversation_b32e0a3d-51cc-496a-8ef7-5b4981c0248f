from DrissionPage import ChromiumPage
import time
import re
from Tool.MongoDBTool import MongoDBTool


class MuseumCrawler:
    def __init__(self, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url


    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')


        t1 = 0.05
        t2 = 1
        t3 = 0.5

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)
        # TODO 具体爬取代码

        # 定义正则表达模式，用以匹配href_tags中<img>标签里的src属性值
        pattern = r'<img.*?src="([^"]*)".*?>'

        #定义变量

        try:
            collection_category = browser.ele(".content-item-list").eles("tag:li")
            i = 0 #指定爬取的第几个文物类
            for element in collection_category[0:1]:
                category_name = element.text
                #储存该文物类别的名字
                element.click()
                #打开该类别的具体文物

                collections_lists = browser.ele(".content-list").eles("tag:li")
                j = 0 #指定爬取该类下第几个文物
                for element in collections_lists[0:1]:
                    img_element = element.ele(".content-list-item-dec-img")
                    img = re.findall(pattern, img_element.html)
                    name = img_element.text
                    era = element.ele(".content-list-item-dec-nav").text

                    element.hover()
                    # 点击的目标需要鼠标悬停等待出现
                    element.click()
                    time.sleep(t2)
                    # 跳转详情
                    dim = browser.ele('.content').ele(".content-specification ").text
                    div = browser.ele('.content').ele(".content-dec")
                    iframe = div.ele('xpath:.//iframe')
                    page = browser.get_current_page()
                    page.switch_to_frame(iframe)
                    ove = page.ele('xpath://p/span')


        except Exception as e:
            print(f'Error while crawl collection information as: {e}')

        print("test over")
        print("test")
        # 存储数据
"""        data = {
            'id': "none",
            'name': name,
            'category': category_name,
            'era': era,
            'origin_museum': origin,
            'source': source,
            'dimensions': dim,
            'material': material,
            'color': color,
            'overview': ove,
            'status': status,
            'img': img,
            'oss_url': oss_url,
        }
"""
if __name__ == "__main__":

    #city_crawl = CityCrawler('江西', '武功山', 'https://travel.qunar.com/p-cs1061868-wugongshan')
    #city_crawl.crawl()

    museum_crawl = MuseumCrawler('https://www.scmuseum.cn/sccms/node/290?subnode=267')
    #四川博物院
    #museum_crawl = MuseumCrawler('https://www.jinshasitemuseum.com/node/197')
    #金沙
    #museum_crawl = MuseumCrawler('https://www.sxd.cn/relics')
    #三星堆
    museum_crawl.crawl()