from DrissionPage import ChromiumPage
from Traversal_China import CityCrawler

# 等待页面加载完成的时间（秒）
t = 0.005

base_url = 'https://travel.qunar.com/place/'
browser = ChromiumPage()

browser.get(base_url)

#定义变量
country = "中国",

#存储城市链接集合
city_link = []

#获取中国各省的名字和html
target = browser.ele(".contbox current")
province = target.eles(".sub_list")
i = 0
province_name = []

#直辖市的sublist

province_name.append(target.ele(".hd").text.replace(' ', '').replace('：', ''))
city = province[0].eles("tag:a")

for element in city:
    city_link.append([province_name[0], element.text, element.href])

while True:
    try:
        i += 1
        name = province[i].ele(".tit").text.replace(' ', '')
        name = name.replace('：', '')
        province_name.append(name)
        for element in province[i].eles("tag:a"):
            city_link.append([province_name[i], element.text, element.href])
    except Exception as e:
        print(f"Error while trying to extrect province name: {e}")
        break

#city_link以 [省份， 市名， 市链接]的变量格式存储

i = 351
for element in city_link[351:]:
    i += 1
    p_name = element[0]
    c_name = element[1]
    c_url = element[2]
    city_crawl = CityCrawler(p_name, c_name, c_url)
    city_crawl.crawl()
    print(i)


"""#贵阳~可可西里
x = 367
for element in city_link[367:382]:
    x += 1
    p_name = element[0]
    c_name = element[1]
    c_url = element[2]
    city_crawl = PageCrawler(p_name, c_name, c_url)
    #city_crawl.crawl()
    print(x)
"""

#24号爬取失败 [0:24] 数据集1
#48号爬取失败 [25:38] 数据集2
#83号爬取失败 [39:83] 数据集3
#111号爬取失败 [112:122]数据集4 [122:156]数据集5
#212号失败  [158:170] [171:212]数据集6
#309失败 [213:250] [251:309] 数据集7
#345 ~ 358 失败[310:345] 数据集8
#382失败，爬取完成 [359:382] 数据集9