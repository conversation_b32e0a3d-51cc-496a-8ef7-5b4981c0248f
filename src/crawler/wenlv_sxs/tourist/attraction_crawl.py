from DrissionPage import ChromiumPage
import time
import re
from Tool.MongoDBTool import MongoDBTool


class AttractionCrawler:
    def __init__(self, province, city, attraction_name, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.attraction_name = attraction_name
        self.city = city
        self.province = province

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')

        # 检查 MongoDB 中是否已经存在该景点的数据
        existing_data = mongodb_tool.find_one('China_attraction', {'attraction_name': self.attraction_name, 'attraction_link': self.base_url})
        if existing_data:
            print(f"景点 {self.attraction_name} 的数据已存在，跳过爬取操作。")
            return

        # 等待页面加载完成的时间（秒）
        t1 = 0.05
        # 常规的sleep防止因为快速爬导致被封
        t2 = 1
        # 涉及跳转页面的部分，设置长时间避免因为加载页面儿过慢导致程序爬不到元素
        t3 = 0.5
        # 涉及翻页的部分，设置中等时间避免因为加载页面儿过慢导致程序爬不到元素

        browser = ChromiumPage()
        browser.get(self.base_url)

        # 定义变量 省
        province = self.province

        # 标题 市
        city_name = self.city

        # 景点名字
        attraction_name = self.attraction_name

        # 景点链接
        attraction_link = self.base_url

        # 评分
        try:
            score = browser.ele('.scorebox clrfix')
            time.sleep(t1)
            scorebox = score.text
        except Exception as e:
            scorebox = ''
            print(f'Error while crawl the information of scorebox: {e}')

        # 简介
        try:
            over = browser.ele('.e_db_content_box')
            time.sleep(t1)
            overview = over.text
        except Exception as e:
            overview = ''
            print(f'Error while crawl the information of overview: {e}')

        # 总结
        try:
            sum = browser.ele('.e_summary_list clrfix')
            time.sleep(t1)
            summary = sum.text
        except Exception as e:
            summary = ''
            print(f'Error while crawl the information of sum: {e}')

        # 门票价格
        try:
            ticket = browser.ele('.b_detail_section b_detail_ticket')
            time.sleep(t1)
            ticket_price = ticket.text
        except Exception as e:
            ticket_price = ''
            print(f'Error while crawl the information of ticket_price: {e}')

        # 交通指南
        try:
            transport = browser.ele('.b_detail_section b_detail_traffic')
            time.sleep(t1)
            transport_guide = transport.text
        except Exception as e:
            transport_guide = ''
            print(f'Error while crawl the information of transport_guide: {e}')

        # 小贴士
        try:
            tip = browser.ele('.b_detail_section b_detail_tips')
            time.sleep(t1)
            tips = tip.text
        except Exception as e:
            tips = ''
            print(f'Error while crawl the information of tips: {e}')

        # 周边推荐html
        recommends = []
        try:
            recommend = browser.ele('.contbox box_padd').eles('.item')
            time.sleep(t1)
            for item in recommend:
                link = item.ele('tag:a')
                time.sleep(t1)
                href = link.href
                recommend_name = item.ele('.t clrfix').ele('tag:a').text
                distance = item.ele('.distance').text
                recommends.append({'recommend': recommend_name, 'distance': distance, 'href': href})
        except Exception as e:
            print(f'Error while crawl the recommend: {e}')

        # 假设transport_image是用来获取网页旅游景点的图片链接
        transport_images = []
        try:
            transport_image = browser.ele('.e_focus_imgbox')
            href_tags = transport_image.eles('tag:li')
            # 定义正则表达模式，用以匹配href_tags中<img>标签里的src属性值
            pattern = r'<img.*?src="([^"]*)".*?>'
            transport_images = []
            for tag in href_tags:
                # 使用正则表达式进行匹配
                match = re.search(pattern, tag.html)
                if match:
                    # 如果匹配成功，提取 src 属性值并添加到 img 列表中
                    src_img = match.group(1)
                    transport_images.append(src_img)
        except Exception as e:
            print(f"Error while crawl the image: {e}")

        # 存储数据
        data = {
            "province": province,
            "city": city_name,
            "attraction_name": attraction_name,
            "attraction_link": attraction_link,
            "scorebox": scorebox,
            "overview": overview,
            "summary": summary,
            "tickets": ticket_price,
            "transport_guide": transport_guide,
            "tips": tips,
            "transport_image": transport_images,
            "recommend_around": recommends
        }

        inserted_id = mongodb_tool.insert_one('China_attraction', data)
        print(f"插入的景点数据ID: {inserted_id}")


if __name__ == "__main__":
    # 以成都春熙路为例
    attraction_crawl = AttractionCrawler('四川', '成都', '春熙路', 'https://travel.qunar.com/p-cs300085-chengdu')
    attraction_crawl.crawl()