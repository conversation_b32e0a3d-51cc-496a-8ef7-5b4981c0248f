from DrissionPage import ChromiumPage
import time
from Tool.MongoDBTool import MongoDBTool
from wenlv.tourist.attraction_crawl import AttractionCrawler

class CityCrawler:
    def __init__(self, province, city, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.city = city
        self.province = province

    def crawl(self):
        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')

        t1 = 0.05
        t2 = 1
        t3 = 0.5

        base_url = self.base_url
        browser = self.browser

        browser.get(base_url)

        province = self.province
        city_name = self.city
        city_link = base_url

        t = True
        ticket = []

        try:
            ticket = browser.ele(".r_ticket_list").eles("tag:a")
        except Exception as e:
            print(f"Error while try to crawl the ticket information: {e}")
            t = False

        col = browser.ele('.navs').ele('tag:li')
        col_num = browser.ele('.navs').eles('tag:li')
        col_len = len(col_num) - 1

        n = 0
        attraction_col = col.next(n)
        while attraction_col.text != "景点" and n < col_len:
            n += 1
            attraction_col = col.next(n)


        list = []
        if attraction_col.text == "景点":
            attraction_col.click()
            time.sleep(t2)

            link_elements = browser.ele('.list_item clrfix').eles('tag:a')
            for element in link_elements:
                if element.text != '' and element.href != '':
                    attraction = {'province': province,
                                  'city': city_name,
                                  'city_url': base_url,
                                  'attraction_name': element.text,
                                  'attraction_url': element.href}

                    list.append(attraction)

                    result = mongodb_tool.find_one('China_attraction_list', attraction)
                    if result is None:
                        inserted_id = mongodb_tool.insert_one('China_attraction_list', attraction)
                        print(f"插入的景点列表数据ID1: {inserted_id}")




            i = 0
            while True and i < 20:
                try:
                    next_arrow = browser.ele('.page next')
                    next_arrow.click()
                    time.sleep(t3)
                    i += 1

                    link_elements = browser.ele('.list_item clrfix').eles('tag:a')
                    for element in link_elements:
                        if element.text != '' and element.href != '':
                            attraction = {'province': province,
                                          'city': city_name,
                                          'city_url': base_url,
                                          'attraction_name': element.text,
                                          'attraction_url': element.href}

                            list.append(attraction)

                            #检查该景点是否已经被爬取
                            result = mongodb_tool.find_one('China_attraction_list', attraction)
                            if result is None:
                                inserted_id = mongodb_tool.insert_one('China_attraction_list', attraction)
                                print(f"插入的景点列表数据ID2: {inserted_id}")


                except Exception as e:
                    print(f"Maybe this is the final page, error are as below: {e}")
                    break

        elif t:

            #部分city页面没有景点页，但是可以通过ticket爬取一些景点信息
            for element in ticket:
                attraction = {'province': province,
                              'city': city_name,
                              'city_url': base_url,
                              'attraction_name': element.text,
                              'attraction_url': element.href}

                list.append(attraction)

                result = mongodb_tool.find_one('China_attraction_list', attraction)
                if result is None:
                    inserted_id = mongodb_tool.insert_one('China_attraction_list', attraction)
                    print(f"插入的景点列表数据ID3: {inserted_id}")


                    #TODO 爬取Ticket的连接打开的网页的景点信息，clas attraction_ticket
        else:
            attraction = {'province': province,
                          'city': city_name,
                          'city_url': base_url,
                          'attraction_name': "none attraction here",
                          'attraction_url': "none attraction href here"}
            result = mongodb_tool.find_one('China_attraction_list', attraction)
            if result is None:
                inserted_id = mongodb_tool.insert_one('China_attraction_list', attraction)
                print(f"插入的景点列表数据ID2: {inserted_id}")

        k = 0
        while k < len(list) and k < 50:
            attraction_crawl = AttractionCrawler(list[k]['province'],
                                                 list[k]['city'],
                                                 list[k]['attraction_name'],
                                                 list[k]['attraction_url'])
            attraction_crawl.crawl()
            k = k + 1

        print("City" + city_name + "crawl complete")

if __name__ == "__main__":

    #city_crawl = CityCrawler('江西', '武功山', 'https://travel.qunar.com/p-cs1061868-wugongshan')
    #city_crawl.crawl()

    city_crawl = CityCrawler('四川', '成都', 'https://travel.qunar.com/p-cs300085-chengdu')
    city_crawl.crawl()