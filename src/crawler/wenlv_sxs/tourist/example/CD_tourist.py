# -*- coding: utf-8 -*-
# @Time    : 2024/6/12 11:19

'''
获取成都相关的数据，然后转换成json格式，保存到MongoDB中
'''

from DrissionPage import ChromiumPage
import json
import time
import re
import os

# 等待页面加载完成的时间（秒）
t = 0.005

base_url = 'https://travel.qunar.com/p-cs300085-cheng<PERSON>'
browser = ChromiumPage()

browser.get(base_url)

# 定义变量
province = "四川省",

# 景点链接
city_link = base_url

#col是城市界面中间切换展示页面的按键栏
col = browser.ele('.navs').ele('tag:li')
#跳转城市指南
city_guide = col.next(1)
city_guide.click()
time.sleep(1) #等待页面加载


# 使用DrissionPage选择器获取数据
# 标题
city_name = browser.ele('.city_name clrfix')
name = city_name.text
time.sleep(t)

# 总结
summary = browser.ele('.desc')
sum = summary.text
time.sleep(t)

# 简介
overview = browser.ele('.b_g_cont')
over = overview.text
time.sleep(t)

# 假设transport_image是用来获取网页旅游景点的图片链接
# TODO: 将数据保存到MongoDB

city_image = overview.html
#定义正则表达模式，用以匹配href_tags中<img>标签里的src属性值
pattern = r'<img.*?src="([^"]*)".*?>'
city_img_src = re.findall(pattern, overview.html)

#该城市景点的名字+链接
col = browser.ele('.navs').ele('tag:li')
#重新爬取一遍col，不然会显示页面刷新元素失效
attraction = col.next(4)
attraction.click()
time.sleep(1)

links = []
link_elements = browser.ele('.list_item clrfix').eles('tag:a')
for element in link_elements:
    if element.text != '' and element.href != '':
        links.append([element.text, element.href])
i = 0
# i用来限制爬取的页面数量，因为景点、美食和酒店的页面都有200页，所以在测试的时候可以在while True后面设置i的上限
while True and i < 20:
    try:
        next_arrow = browser.ele('.page next')
        next_arrow.click()
        time.sleep(1) #等待页面加载
        i += 1

        link_elements = browser.ele('.list_item clrfix').eles('tag:a')
        for element in link_elements:
            if element.text != '' and element.href != '':
                links.append([element.text, element.href])

    except Exception as e:
        print(f"Maybe this is the final page, error are as below: {e}")
        break


#跳转美食页面
col = browser.ele('.navs').ele('tag:li')
#重新爬取一遍col，不然会显示页面刷新元素失效
cate = col.next(5)
cate.click()

local_cate = []
local_element = browser.ele('.c_food').eles('.list')
for element in local_element:
    cate_element = element.eles('.item')
    for e in cate_element:
        cate2_element = e.ele('tag:a')
        local_cate.extend([e.text, cate2_element.href]) #local_cate存储“当地美食”的元素

#餐厅
res = []
r_elements = browser.ele('.list_item clrfix').eles('tag:a')
for element in r_elements:
    if element.text != '' and element.href != '':
        res.append([element.text, element.href])
j = 0
#限制爬取页数
while True and j < 20:
    try:
        next_arrow = browser.ele('.page next')
        next_arrow.click()
        time.sleep(1)  # 等待页面加载
        j += 1

        r_elements = browser.ele('.list_item clrfix').eles('tag:a')
        for element in r_elements:
            if element.text != '' and element.href != '':
                res.append([element.text, element.href])

    except Exception as e:
        print(f"Maybe this is the final page, error are as below: {e}")
        break

#跳转酒店页面
col = browser.ele('.navs').ele('tag:li')
#重新爬取一遍col，不然会显示页面刷新元素失效
hotel_link = col.next(6)
hotel_link.click()

hotel = []
h_elements = browser.ele('.list_item clrfix').eles('tag:a')
for element in h_elements:
    if element.text != '' and element.href != '':
        hotel.append([element.text, element.href])
h = 0
#限制爬取页数
while True and h < 20:
    try:
        next_arrow = browser.ele('.page next')
        next_arrow.click()
        time.sleep(1)  # 等待页面加载
        h += 1

        h_elements = browser.ele('.list_item clrfix').eles('tag:a')
        for element in h_elements:
            if element.text != '' and element.href != '':
                hotel.append([element.text, element.href])

    except Exception as e:
        print(f"Maybe this is the final page, error are as below: {e}")
        break

#购物
col = browser.ele('.navs').ele('tag:li')
#重新爬取一遍col，不然会显示页面刷新元素失效
shopping_link = col.next(7)
shopping_link.click()

#推荐特产
specialty = []
specialty_element = browser.ele('.c_food').eles('.list')
for element in specialty_element:
    s_element = element.eles('.item')
    for e in s_element:
        s2_element = e.ele('tag:a')
        specialty.extend([e.text, s2_element.href]) #specialty存储“当地特产”的元素
print(specialty)

#推荐商业街、商店
shop = []
s_elements = browser.ele('.list_item clrfix').eles('tag:a')
for element in s_elements:
    if element.text != '' and element.href != '':
        shop.append([element.text, element.href])
s = 0
#限制爬取页数
while True and s < 20:
    try:
        next_arrow = browser.ele('.page next')
        next_arrow.click()
        time.sleep(1)  # 等待页面加载
        s += 1

        s_elements = browser.ele('.list_item clrfix').eles('tag:a')
        for element in s_elements:
            if element.text != '' and element.href != '':
                shop.append([element.text, element.href])

    except Exception as e:
        print(f"Maybe this is the final page, error are as below: {e}")
        break

# 存储数据
data = {
    "province": province,
    "city_link": city_link,
    "city_name": name,
    "summary": sum,
    "overview": over,
    "city_image": city_img_src,
    "local_cate": local_cate,
    "specialty": specialty,
}

data_around = {
    "attraction": links,
    "restaurant": res,
    "hotel": hotel,
    "shop": shop,
}

# 打印json格式的数据
print(json.dumps(data, ensure_ascii=False, indent=4))

# 将数据转换为JSON格式的字符串
json_str = json.dumps(data, ensure_ascii=False, indent=4)
json_str2 = json.dumps(data_around, ensure_ascii=False, indent=4)

# 指定保存到的文件路径
file_directory = r'F:\MongoDB\wenlv'
file_name = name.split('\n')[0]
file_path = os.path.join(file_directory, file_name + '.json')

file_name2 = file_name + "周边"
file_path2 = os.path.join(file_directory, file_name2 + '.json')

# 将JSON字符串写入文件
with open(file_path, 'w', encoding='utf-8') as f:
    f.write(json_str)

with open(file_path2, 'w', encoding='utf-8') as f:
    f.write(json_str2)

print(f"JSON数据已保存到文件: {file_path}")
print(f"JSON数据已保存到文件: {file_path2}")
