# -*- coding: utf-8 -*-
# @Time    : 2024/6/12 11:19

'''
获取春熙路景点相关的数据，然后转换成json格式，保存到MongoDB中
'''

from DrissionPage import ChromiumPage
import json
import time
import re
import os

# 等待页面加载完成的时间（秒）
t = 0.005

base_url = 'https://travel.qunar.com/p-oi18700089-chunxilu'
browser = ChromiumPage()

browser.get(base_url)

# 定义变量
province = "四川省",
city = "成都市",

# 景点链接
attraction_link = base_url

# 使用DrissionPage选择器获取数据
# 标题
attraction_name = browser.ele('.b_title clrfix')
time.sleep(t)

# 评分
scorebox = browser.ele('.scorebox clrfix')
time.sleep(t)

# 简介
overview = browser.ele('.e_db_content_box')
time.sleep(t)

# 总结
summary = browser.ele('.e_summary_list clrfix')
time.sleep(t)

# 门票价格
ticket_price = browser.ele('.b_detail_section b_detail_ticket')
time.sleep(t)

# 交通指南
transport_guide = browser.ele('.b_detail_section b_detail_traffic')
time.sleep(t)

# 小贴士
tips = browser.ele('.b_detail_section b_detail_tips')
time.sleep(t)

# 周边推荐html
recommend = browser.ele('.contbox box_padd').eles('.item')
time.sleep(t)
i = 0
recommends = []
for item in recommend:
    link = item.ele('tag:a')
    time.sleep(t)
    href = link.href
    recommends.append(recommend[i].text + '\n ' + href)
    i += 1


# 假设transport_image是用来获取网页旅游景点的图片链接
# TODO: 将数据保存到MongoDB

transport_image = browser.ele('.e_focus_imgbox')
href_tags = transport_image.eles('tag:li')
#定义正则表达模式，用以匹配href_tags中<img>标签里的src属性值
pattern = r'<img.*?src="([^"]*)".*?>'
transport_images = []
for tag in href_tags:
    # 使用正则表达式进行匹配
    match = re.search(pattern, tag.html)
    if match:
        # 如果匹配成功，提取 src 属性值并添加到 img 列表中
        src_img = match.group(1)
        transport_images.append(src_img)

# 存储数据
data = {
    "province": province,
    "city": city,
    "attraction_link": attraction_link,
    "attraction_name": attraction_name.text,

    "scorebox": scorebox.text,
    "overview": overview.text,
    "summary": summary.text,
    "tickets": ticket_price.text,
    "transport_guide": transport_guide.text,
    "tips": tips.text,
    "transport_image": transport_images,
    "recommend_around": recommends
}

# 打印json格式的数据
print(json.dumps(data, ensure_ascii=False, indent=4))

# 将数据转换为JSON格式的字符串
json_str = json.dumps(data, ensure_ascii=False, indent=4)

# 指定保存到的文件路径
file_directory = r'F:\MongoDB\wenlv'
file_name = attraction_name.text.split('\n')[0]
file_path = os.path.join(file_directory, file_name + '.json')

# 将JSON字符串写入文件
with open(file_path, 'w', encoding='utf-8') as f:
    f.write(json_str)

print(f"JSON数据已保存到文件: {file_path}")
