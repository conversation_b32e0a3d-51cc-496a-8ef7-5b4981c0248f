本栏中的代码以”中国-省份-城市-景点“的顺序爬取旅游相关信息
example中以”宽窄巷子“和”春熙路“作为例子，爬取具体景点信息

traversal_China.py: 单独运行，不调用其他部分代码，用以按照”中国-省份-城市-景点“的顺序遍历，输出的信息只有景点的基本信息和该景点的网址。
优点是可以快速遍历国内的旅游景点，方便查看景点数量、某省某市有哪些景点。

China_attraction_count.py: 遍历中国的各个省份，将country，province 和 province_url信息作为输入，调用City_tourist.py 或者attraction_crawl.py。
注意: 虽然代码能遍历全部省份，但由于网站可能卡死或网站部分省、市、景点其并没有具体信息，因此在这段代码的后端，通过设置i控制循环的次数、以及调用的存储全部省份信息的列表
中的第几个元素作为开始，方便实际操作

City_tourist.py: 爬取城市的具体信息，包含的信息参考下表：
data = {
            "province": province,  省份
            "city_name": city_name,  城市
            "city_link": city_link, 城市的网址
            "summary": sum,  介绍
            "overview": over,  概述
            "city_image": city_img_src,  城市相关图片
            "local_cate": local_cate,   特色美食
            "specialty": specialty,    特产
            "attraction": filtered_links,   景点
            "restaurant": filtered_res,   餐厅
            "hotel": filtered_hotel,   酒店
            "shop": filtered_shop,   商店
        }
其中图片、特色美食、特产、景点、餐厅、酒店、商店 都以新的表进行存储，包含一些其基础信息


attraction_crawl.py: 爬取具体的景点信息，包含的信息参考下表：
data = {
            "province": province, 省
            "city": city_name,  市
            "attraction_name": attraction_name,  景点名
            "attraction_link": attraction_link,  景点网址
            "scorebox": scorebox,  评分
            "overview": overview,  介绍
            "summary": summary,  概述
            "tickets": ticket_price,  门票
            "transport_guide": transport_guide,  交通指南
            "tips": tips,   小贴士
            "transport_image": transport_images,   景点图片
            "recommend_around": recommends  相关景点、附近的美食、酒店
        }
