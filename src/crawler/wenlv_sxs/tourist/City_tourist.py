# -*- coding: utf-8 -*-
# @Time    : 2024/6/12 11:19

'''
获取成都相关的数据，然后转换成json格式，保存到MongoDB中
'''

from DrissionPage import ChromiumPage
import json
import time
import re
from Tool.MongoDBTool import MongoDBTool


class PageCrawler:
    def __init__(self, province, city, base_url):
        self.browser = ChromiumPage()
        self.base_url = base_url
        self.city = city
        self.province = province

    def crawl(self):

        global filtered_links, filtered_res, filtered_shop, filtered_hotel
        filtered_shop = []
        filtered_hotel = []
        filtered_res = []
        filtered_links = []

        mongodb_tool = MongoDBTool(username='root', password='root', database='wenlv')
        # 连接数据库

        # 等待页面加载完成的时间（秒）
        t1 = 0.05
        # 常规的sleep防止因为快速爬导致被封
        t2 = 2
        # 涉及跳转页面的部分，设置长时间避免因为加载页面儿过慢导致程序爬不到元素
        t3 = 1
        # 涉及翻页的部分，设置中等时间避免因为加载页面儿过慢导致程序爬不到元素

        base_url = self.base_url
        browser = ChromiumPage()

        browser.get(base_url)

        # 定义变量 省
        province = self.province

        # 标题 市
        city_name = self.city

        # 城市链接
        city_link = base_url

        # col是城市界面中间切换展示页面的按键栏
        col = browser.ele('.navs').ele('tag:li')
        col_num = browser.ele('.navs').eles('tag:li')
        col_len = len(col_num) - 1
        # 跳转城市指南
        city_guide = col.next(1)
        city_guide.click()
        time.sleep(6)  # 等待页面加载

        # 使用DrissionPage选择器获取数据

        # 总结
        summary = browser.ele('.desc')
        sum = summary.text
        time.sleep(t1)

        # 简介
        overview = browser.ele('.b_g_cont')
        over = overview.text
        time.sleep(t1)

        # 假设transport_image是用来获取网页旅游景点的图片链接
        # TODO: 将数据保存到MongoDB

        city_image = overview.html
        # 定义正则表达模式，用以匹配href_tags中<img>标签里的src属性值
        pattern = r'<img.*?src="([^"]*)".*?>'
        city_img_src = re.findall(pattern, overview.html)

        # 该城市景点的名字+链接
        col = browser.ele('.navs').ele('tag:li')
        # 重新爬取一遍col，不然会显示页面刷新元素失效
        n = 0
        # 舔加n，从col第二个next开始遍历，因为该网站下部分地区没有交通等栏目
        attraction = col.next(n)
        while attraction.text != "景点" and n < col_len:
            n += 1
            attraction = col.next(n)
        if attraction.text == "景点":
            attraction.click()
            time.sleep(t2)

        links = []
        attraction = []
        link_elements = browser.ele('.list_item clrfix').eles('tag:a')
        for element in link_elements:
            all_src = re.findall(pattern, element.html)
            attraction_src = all_src[0] if all_src else None

            if element.text != '':
                attraction_name = element.text

            if element.href != '':
                attraction_url = element.href

            links.append({'attraction_name': attraction_name,
                          'attraction_url': attraction_url,
                          'attraction_img': attraction_src,
                          })
            filtered_links = [link for link in links if link['attraction_img']]
        # TODO

        i = 0
        # i用来限制爬取的页面数量，因为景点、美食和酒店的页面都有200页，所以在测试的时候可以在while True后面设置i的上限
        while True and i < 10:
            try:
                next_arrow = browser.ele('.page next')
                next_arrow.click()
                time.sleep(t3)  # 等待页面加载
                i += 1

                link_elements = browser.ele('.list_item clrfix').eles('tag:a')
                for element in link_elements:
                    all_src = re.findall(pattern, element.html)
                    attraction_src = all_src[0] if all_src else None

                    if element.text != '':
                        attraction_name = element.text

                    if element.href != '':
                        attraction_url = element.href

                    links.append({'attraction_name': attraction_name,
                                  'attraction_url': attraction_url,
                                  'attraction_img': attraction_src,
                                  })
                    filtered_links = [link for link in links if link['attraction_img']]
            # TODO

            except Exception as e:
                print(f"Maybe this is the final page, error are as below {e}")
                break

        # 跳转美食页面
        col = browser.ele('.navs').ele('tag:li')
        # 重新爬取一遍col，不然会显示页面刷新元素失效
        n = 0
        cate = col.next(n)
        while cate.text != "美食" and n < col_len:
            n += 1
            cate = col.next(n)
        if cate.text == "美食":
            cate.click()
            time.sleep(t2)

        local_cate = []
        try:
            local_element = browser.ele('.c_food').eles('.list')
            for element in local_element:
                cate_element = element.eles('.item')
                for e in cate_element:
                    cate2_element = e.ele('tag:a')
                    local_cate.append({'cute_name': e.text, 'cute_url': cate2_element.href})  # local_cate存储“当地美食”的元素
        except Exception as e:
            print(f"Error while crawl the local cute: {e}")

        # 餐厅
        res = []
        try:
            r_elements = browser.ele('.list_item clrfix').eles('tag:a')
            for element in r_elements:
                all_res_src = re.findall(pattern, element.html)
                res_src = all_res_src[0] if all_res_src else None

                if element.text != '':
                    res_name = element.text

                if element.href != '':
                    res_url = element.href

                res.append({'restaurant_name': res_name,
                            'restaurant_url': res_url,
                            'restaurant_img': res_src
                            })

                filtered_res = [link for link in res if link['restaurant_img']]
            j = 0
            # 限制爬取页数
            while True and j < 10:
                try:
                    next_arrow = browser.ele('.page next')
                    next_arrow.click()
                    time.sleep(t3)  # 等待页面加载
                    j += 1

                    r_elements = browser.ele('.list_item clrfix').eles('tag:a')
                    for element in r_elements:
                        all_res_src = re.findall(pattern, element.html)
                        res_src = all_res_src[0] if all_res_src else None

                        if element.text != '':
                            res_name = element.text

                        if element.href != '':
                            res_url = element.href

                        res.append({'restaurant_name': res_name,
                                    'restaurant_url': res_url,
                                    'restaurant_img': res_src
                                    })

                        filtered_res = [link for link in res if link['restaurant_img']]

                except Exception as e:
                    print(f"Maybe this is the final page, error are as below: {e}")
                    break
        except Exception as e:
            print(f"Error while crawl the restaurant page: {e}")

        # 跳转酒店页面
        col = browser.ele('.navs').ele('tag:li')
        # 重新爬取一遍col，不然会显示页面刷新元素失效

        n = 0
        hotel_link = col.next(n)
        while hotel_link.text != "酒店" and n < col_len:
            n += 1
            hotel_link = col.next(n)
        if hotel_link.text == "酒店":
            hotel_link.click()
            time.sleep(t2)

        hotel = []
        try:
            h_elements = browser.ele('.list_item clrfix').eles('tag:a')
            for element in h_elements:
                all_h_src = re.findall(pattern, element.html)
                hotel_src = all_h_src[0] if all_h_src else None

                if element.text != '':
                    hotel_name = element.text

                if element.href != '':
                    hotel_url = element.href

                hotel.append({'hotel_name': hotel_name,
                              'hotel_url': hotel_url,
                              'hotel_img': hotel_src
                              })

                filtered_hotel = [link for link in hotel if link['hotel_img']]

            h = 0
            # 限制爬取页数
            while True and h < 10:
                try:
                    next_arrow = browser.ele('.page next')
                    next_arrow.click()
                    time.sleep(t3)  # 等待页面加载
                    h += 1

                    h_elements = browser.ele('.list_item clrfix').eles('tag:a')
                    for element in h_elements:
                        all_h_src = re.findall(pattern, element.html)
                        hotel_src = all_h_src[0] if all_h_src else None

                        if element.text != '':
                            hotel_name = element.text

                        if element.href != '':
                            hotel_url = element.href

                        hotel.append({'hotel_name': hotel_name,
                                      'hotel_url': hotel_url,
                                      'hotel_img': hotel_src
                                      })

                        filtered_hotel = [link for link in hotel if link['hotel_img']]

                except Exception as e:
                    print(f"Maybe this is the final page, error are as below: {e}")
                    break
        except Exception as e:
            print(f"Error while crawl the hotel page: {e}")

        # 购物
        col = browser.ele('.navs').ele('tag:li')
        # 重新爬取一遍col，不然会显示页面刷新元素失效
        n = 0
        shopping_link = col.next(n)
        while shopping_link.text != "购物" and n < col_len:
            n += 1
            shopping_link = col.next(n)
        if shopping_link.text == "购物":
            shopping_link.click()
            time.sleep(t2)

        specialty = []
        shop = []
        try:
            try:
                # 推荐特产
                specialty_element = browser.ele('.c_food').eles('.list')
                for element in specialty_element:
                    s_element = element.eles('.item')
                    for e in s_element:
                        s2_element = e.ele('tag:a')
                        specialty.append(
                            {'specialty_name': e.text, 'specialty_url': s2_element.href})  # specialty存储“当地特产”的元素
            except Exception as e:
                print(f"Error while crawl local specialty: {e}")

            # 推荐商业街、商店
            s_elements = browser.ele('.list_item clrfix').eles('tag:a')
            for element in s_elements:
                all_s_src = re.findall(pattern, element.html)
                shop_src = all_s_src[0] if all_s_src else None

                if element.text != '':
                    shop_name = element.text

                if element.href != '':
                    shop_url = element.href

                shop.append({'shop_name': shop_name,
                             'shop_url': shop_url,
                             'shop_img': shop_src
                             })

                filtered_shop = [link for link in shop if link['shop_img']]

            s = 0
            # 限制爬取页数
            while True and s < 10:
                try:
                    next_arrow = browser.ele('.page next')
                    next_arrow.click()
                    time.sleep(t3)  # 等待页面加载
                    s += 1

                    s_elements = browser.ele('.list_item clrfix').eles('tag:a')
                    for element in s_elements:
                        all_s_src = re.findall(pattern, element.html)
                        shop_src = all_s_src[0] if all_s_src else None

                        if element.text != '':
                            shop_name = element.text

                        if element.href != '':
                            shop_url = element.href

                        shop.append({'shop_name': shop_name,
                                     'shop_url': shop_url,
                                     'shop_img': shop_src
                                     })

                        filtered_shop = [link for link in shop if link['shop_img']]

                except Exception as e:
                    print(f"Maybe this is the final page, error are as below: {e}")
                    break
        except Exception as e:
            print(f"Error while crawl the shopping page: {e}")

        # 存储数据
        data = {
            "province": province,
            "city_name": city_name,
            "city_link": city_link,
            "summary": sum,
            "overview": over,
            "city_image": city_img_src,
            "local_cate": local_cate,
            "specialty": specialty,
            "attraction": filtered_links,
            "restaurant": filtered_res,
            "hotel": filtered_hotel,
            "shop": filtered_shop,
        }

        # 打印json格式的数据
        print(json.dumps(data, ensure_ascii=False, indent=4))

        """

        # 将数据转换为JSON格式的字符串
        json_str = json.dumps(data, ensure_ascii=False, indent=4)
        json_str2 = json.dumps(data_around, ensure_ascii=False, indent=4)

        # 指定保存到的文件路径
        file_directory = r'F:\MongoDB\wenlv'
        file_name = city_name
        file_path = os.path.join(file_directory, file_name + '.json')

        file_name2 = file_name + "周边"
        file_path2 = os.path.join(file_directory, file_name2 + '.json')

        # 将JSON字符串写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(json_str)

        with open(file_path2, 'w', encoding='utf-8') as f:
            f.write(json_str2)

        print(f"JSON数据已保存到文件: {file_path}")
        print(f"JSON数据已保存到文件: {file_path2}")
        """
        inserted_id = mongodb_tool.insert_one('city', data)
        print(f"插入的data数据ID: {inserted_id}")


if __name__ == "__main__":
    # 以成都为例
    city_crawl = PageCrawler('四川', '成都', 'https://travel.qunar.com/p-cs300085-chengdu')
    city_crawl.crawl()
