# -*- coding: utf-8 -*-
# @Time    : 2024/7/4 9:44
# author    zlj
import json
import argparse


def read_labels(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip()]


def reorder_categories(dataset_path, labels_file, output_path):
    # Read the dataset
    with open(dataset_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # Read the new category order from the labels file
    new_labels = read_labels(labels_file)
    new_category_map = {label: i + 1 for i, label in enumerate(new_labels)}

    # Create a mapping from old category IDs to new category IDs
    old_to_new_category_id = {}
    for category in data["categories"]:
        if category["name"] in new_category_map:
            old_to_new_category_id[category["id"]] = new_category_map[category["name"]]
        else:
            # If the category is not in the new labels list, assign a new ID
            new_id = len(new_category_map) + 1
            new_category_map[category["name"]] = new_id
            old_to_new_category_id[category["id"]] = new_id

    # Update categories with new IDs
    new_categories = []
    for category in data["categories"]:
        new_id = new_category_map[category["name"]]
        new_category = category.copy()
        new_category["id"] = new_id
        new_categories.append(new_category)

    # Sort categories by new ID
    new_categories = sorted(new_categories, key=lambda x: x["id"])

    # Update annotations with new category IDs
    for annotation in data["annotations"]:
        old_category_id = annotation["category_id"]
        annotation["category_id"] = old_to_new_category_id[old_category_id]

    # Update the dataset with new categories and annotations
    data["categories"] = new_categories

    # Save the updated dataset
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def main():
    # 创建解析器并设置描述信息为中文
    parser = argparse.ArgumentParser(description="根据指定的标签文件对COCO数据集类别进行重新排序。")

    # 添加参数，并将help信息翻译成中文
    parser.add_argument("dataset_path", type=str, help="COCO数据集JSON文件的路径。")
    parser.add_argument("labels_file", type=str, help="指定新类别顺序的标签文件路径。")
    parser.add_argument("output_path", type=str, help="保存重新排序后COCO数据集JSON文件的路径。")

    args = parser.parse_args()

    reorder_categories(args.dataset_path, args.labels_file, args.output_path)


if __name__ == "__main__":
    main()
