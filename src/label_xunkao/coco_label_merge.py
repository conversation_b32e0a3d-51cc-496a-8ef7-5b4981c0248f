# -*- coding: utf-8 -*-
# @Time    : 2024/6/27 14:11
import json
import argparse


def load_config(config_path):
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    return config


def validate_coco(coco_data):
    required_keys = ["images", "annotations", "categories"]
    for key in required_keys:
        if key not in coco_data:
            raise ValueError(f"Missing required key '{key}' in COCO data.")

    for image in coco_data["images"]:
        if "id" not in image:
            raise ValueError("Missing 'id' in one of the images.")

    for annotation in coco_data["annotations"]:
        if (
            "id" not in annotation
            or "image_id" not in annotation
            or "category_id" not in annotation
        ):
            raise ValueError(
                "Missing 'id', 'image_id', or 'category_id' in one of the annotations."
            )

    for category in coco_data["categories"]:
        if "id" not in category or "name" not in category:
            raise ValueError("Missing 'id' or 'name' in one of the categories.")

    print("COCO file validation passed.")


def merge_labels(input_file, output_file, config_path):
    config = load_config(config_path)
    merge_rules = config["merge_rules"]

    # 读取并验证COCO格式的标签文件
    with open(input_file, "r", encoding="utf-8") as f:
        data = json.load(f)

    validate_coco(data)

    # 构建old_id到new_id和new_name的映射
    id_map = {}
    name_map = {}
    for rule in merge_rules:
        new_id = rule["new_id"]
        new_name = rule["new_name"]
        for old_id in rule["old_ids"]:
            id_map[old_id] = new_id
        name_map[new_id] = new_name

    # 更新annotations中的category_id
    for annotation in data["annotations"]:
        old_id = annotation["category_id"]
        if old_id in id_map:
            annotation["category_id"] = id_map[old_id]

    # 更新categories中的id和name
    new_categories = []
    existing_ids = set()
    for category in data["categories"]:
        if category["id"] in id_map:
            new_id = id_map[category["id"]]
            if new_id not in existing_ids:
                new_category = {"id": new_id, "name": name_map[new_id]}
                new_categories.append(new_category)
                existing_ids.add(new_id)
        else:
            new_categories.append(category)

    data["categories"] = new_categories

    # 保存并验证更新后的标签文件
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    with open(output_file, "r", encoding="utf-8") as f:
        updated_data = json.load(f)

    validate_coco(updated_data)

    print(f"Labels merged and saved to {output_file}")


# demo 运行脚本命令
#  python coco_label_merge.py  --input_file  labels_0704.json  --output_file  labels_0704_mergeid.json  --config_file  coco_label_merge.json
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="根据配置合并COCO标签。")
    parser.add_argument(
        "--input_file",
        type=str,
        default="coco_annotations.json",
        help="输入COCO JSON文件的路径。",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default="merged_annotations.json",
        help="保存输出JSON文件的路径。",
    )
    parser.add_argument(
        "--config_file", type=str, default="config.json", help="配置JSON文件的路径。"
    )
    args = parser.parse_args()

    merge_labels(args.input_file, args.output_file, args.config_file)
