# -*- coding: utf-8 -*-
# @Time    : 2024/7/8 14:45
# author zlj

import json
import argparse


def load_config(config_file):
    with open(config_file, "r", encoding="utf-8") as f:
        return json.load(f)


def swap_category_names(input_file, output_file, config_file, to_english=True):
    # 加载配置文件
    config = load_config(config_file)
    translations = config["translations"]

    # 读取COCO JSON文件
    with open(input_file, "r", encoding="utf-8") as f:
        data = json.load(f)

    # 创建一个查找字典，用于快速查找翻译
    lookup = {
        item["chinese" if to_english else "english"]: item["english" if to_english else "chinese"]
        for item in translations.values()
    }

    # 遍历并更新categories
    for category in data["categories"]:
        if category["name"] in lookup:
            category["name"] = lookup[category["name"]]

    # 保存修改后的JSON文件
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


"""
 使用demo
 python coco_label_language_switcher.py  --input_file  labels_0704.json  --output_file  labels_0704_language_switcher.json  --config_file  coco_label_language_switcher_config.json  --to_english
"""


def main():
    parser = argparse.ArgumentParser(description="COCO标注文件中类别名称的中英文转换")
    parser.add_argument(
        "--input_file",
        type=str,
        default="input_coco.json",
        help="输入的COCO JSON文件路径",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default="output_coco.json",
        help="输出的COCO JSON文件路径",
    )
    parser.add_argument("--config_file", type=str, default="config.json", help="配置文件路径")
    parser.add_argument(
        "--to_english",
        action="store_true",
        help="如果设置此参数，将从中文转换为英文；否则从英文转换为中文",
    )

    args = parser.parse_args()

    swap_category_names(args.input_file, args.output_file, args.config_file, args.to_english)
    print(f"转换完成。结果已保存到 {args.output_file}")


if __name__ == "__main__":
    main()
