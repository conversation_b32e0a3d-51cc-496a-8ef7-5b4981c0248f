# -*- coding: utf-8 -*-
# @Time    : 2024/7/2 9:28

import json
import os
import argparse


def read_labels(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip()]


def merge_coco_datasets(directories, labels_file=None):
    merged_data = {
        "info": {},
        "licenses": [],
        "categories": [],
        "images": [],
        "annotations": [],
    }

    # Read labels from file and create initial category mapping
    if labels_file and os.path.exists(labels_file):
        labels = read_labels(labels_file)
        category_map = {label: i + 1 for i, label in enumerate(labels)}
        next_category_id = len(labels) + 1
    else:
        category_map = {}
        next_category_id = 1

    image_map = {}
    processed_categories = set()  # To keep track of processed categories

    file_paths = []
    for directory in directories:
        for file_name in os.listdir(directory):
            if file_name.endswith(".json"):
                file_paths.append(os.path.join(directory, file_name))

    for file_index, file_path in enumerate(file_paths):
        # 提取文件名前缀
        file_name_prefix = os.path.splitext(os.path.basename(file_path))[0]

        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        # 保留第一个文件的info和licenses
        if not merged_data["info"]:
            merged_data["info"] = data["info"]
            merged_data["licenses"] = data["licenses"]

        # 步骤1：整合categories
        file_category_map = {}
        for category in data["categories"]:
            if category["name"] not in category_map:
                category_map[category["name"]] = next_category_id
                next_category_id += 1

            new_id = category_map[category["name"]]
            file_category_map[category["id"]] = new_id

            # Only add the category if it hasn't been processed before
            if category["name"] not in processed_categories:
                processed_categories.add(category["name"])
                new_category = category.copy()
                new_category["id"] = new_id
                merged_data["categories"].append(new_category)

        # 步骤2：整合images
        old_image_id_to_new = {}
        for image in data["images"]:
            old_id = image["id"]
            unique_id = f"{file_index}_{old_id}"
            image["file_name"] = file_name_prefix + "/" + image["file_name"]
            if unique_id not in image_map:
                new_id = len(merged_data["images"]) + 1
                image_map[unique_id] = new_id
                old_image_id_to_new[old_id] = new_id
                image["id"] = new_id
                image["source_file"] = os.path.basename(file_path)
                merged_data["images"].append(image)
            else:
                old_image_id_to_new[old_id] = image_map[unique_id]

        # 步骤3：整合annotations
        for annotation in data["annotations"]:
            old_image_id = annotation["image_id"]
            unique_image_id = f"{file_index}_{old_image_id}"
            new_image_id = image_map[unique_image_id]

            annotation["id"] = len(merged_data["annotations"]) + 1
            annotation["image_id"] = new_image_id

            old_category_id = annotation["category_id"]
            if old_category_id in file_category_map:
                annotation["category_id"] = file_category_map[old_category_id]
            else:
                print(
                    f"Warning: Category ID {old_category_id} not found in file {file_path}. Skipping this annotation."
                )
                continue

            merged_data["annotations"].append(annotation)

    # Ensure categories are sorted by ID
    merged_data["categories"] = sorted(merged_data["categories"], key=lambda x: x["id"])

    return merged_data


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="合并多个COCO数据集文件")
    parser.add_argument(
        "--directories",
        type=str,
        nargs="+",
        required=True,
        help="包含COCO数据集文件的目录列表",
    )
    parser.add_argument("--labels_file", type=str, help="标签排序合集文件路径")
    parser.add_argument(
        "--output_file", type=str, required=True, help="输出合并后的COCO数据集文件路径"
    )

    args = parser.parse_args()

    merged_dataset = merge_coco_datasets(args.directories, args.labels_file)

    # 保存合并后的数据集
    with open(args.output_file, "w", encoding="utf-8") as f:
        json.dump(merged_dataset, f, ensure_ascii=False, indent=2)
