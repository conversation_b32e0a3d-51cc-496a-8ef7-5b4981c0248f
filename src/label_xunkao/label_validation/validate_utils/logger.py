import logging


def setup_logger(logger_name: str):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.INFO)

    # 创建一个 FileHandler，设置延迟打开文件
    file_handler = logging.FileHandler(
        f"{logger_name}.error.log", mode="a", encoding="utf-8", delay=True
    )

    # 设置日志格式
    formatter = logging.Formatter(
        "%(levelname)s::%(name)s::%(asctime)s::%(filename)s,line %(lineno)s, in %(funcName)s(),msg:%(message)s"
    )
    file_handler.setFormatter(formatter)

    # 将 FileHandler 添加到 logger
    logger.addHandler(file_handler)

    return logger
