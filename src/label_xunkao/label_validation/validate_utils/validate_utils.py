from datetime import datetime
import json
import os
import re
import shutil

from shapely import Polygon


class ValidateUtils:

    @staticmethod
    def validate_prefix_folder_name(prefix_folder_name: str) -> bool:
        # 文件夹的规范： v.[时间].r[数字 三位]_s[数字 三位].i[数字 两位]_img_cnt_[图片量-7位] + 对应的后缀
        prefix_pattern = r"^v\.\d{8}\.r_\d{3}\.s_\d{2}\.i_\d{3}\.img_cnt_\d{7}$"
        # 文件夹的规范： v.[时间].r[数字 三位]_s[数字 三位].i[数字 两位].p_[数字两位]_img_cnt_[图片量-7位] + 对应的后缀
        prefix_pattern_with_part = r"^v\.\d{8}\.r_\d{3}\.s_\d{2}\.i_\d{3}\.p_\d{2}\.img_cnt_\d{7}$"
        try:
            date_string = prefix_folder_name[2:10]
        except Exception:
            return False

        return ValidateUtils.validate_date_string(date_string) and bool(
            re.match(prefix_pattern, prefix_folder_name)
            or re.match(prefix_pattern_with_part, prefix_folder_name)
        )

    @staticmethod
    def validate_date_string(date_string: str) -> bool:
        # 正则表达式验证 8 位数字
        date_pattern = r"^\d{8}$"
        if not re.match(date_pattern, date_string):
            return False

        # 将字符串转换为 datetime 对象
        try:
            date = datetime.strptime(date_string, "%Y%m%d")
        except ValueError:
            return False

        # 获取当前日期
        current_date = datetime.now()

        # 检查日期是否在 20200101 和当前日期之间
        start_date = datetime(2020, 1, 1)
        return start_date <= date <= current_date

    @staticmethod
    def validate_folder_name(folder_name: str) -> bool:
        # 文件夹名的前缀符合规范且后缀为.formal
        try:
            prefix_folder_name = folder_name[0:-7]
            suffix_folder_name = folder_name[-7:]
        except Exception:
            # 报错表示位数不满足直接算错误
            return False
        suffix_pattern = r"\.formal$"
        return ValidateUtils.validate_prefix_folder_name(prefix_folder_name) and bool(
            re.match(suffix_pattern, suffix_folder_name)
        )

    @staticmethod
    def load_json_file(file_path: str) -> json:
        assert os.path.exists(file_path), f"{file_path}路径的文件不存在"
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
        except Exception:
            msg = f"读取json文件{file_path}失败"
            print(msg)
            raise Exception(msg)
        return data

    @staticmethod
    def extract_image_count_from_path(path: str, pattern: str) -> int:
        match = re.search(pattern, path)
        return int(match.group(1)) if match else 0

    @staticmethod
    def check_images_format(images_dir: str) -> list:
        errors = []
        for f in os.listdir(images_dir):
            if not f.endswith(".jpg"):
                errors.append(f"'{f}' 不是 jpg 格式的图片,不应该在images文件夹中")
        return errors

    @staticmethod
    def check_spaces_in_parent_dirs(base_dir: str):
        errors = []
        for root, dirs, files in os.walk(base_dir):
            for name in dirs:
                parent_dir = os.path.dirname(os.path.join(root, name))
                if " " in os.path.basename(parent_dir):
                    errors.append(f"文件夹名称中包含空格: '{parent_dir}'")
                    break
        return errors

    @staticmethod
    def check_versions_completeness(
        coco_label_dir: str, raw_label_dir: str, required_versions: list
    ) -> list:
        errors = []
        coco_versions = set()  # 初始化 coco_versions

        # 获取所有coco_label下的JSON文件
        coco_files = [f for f in os.listdir(coco_label_dir) if f.endswith(".json")]
        for f in coco_files:
            try:
                coco_versions.add(re.search(r"\.(\d{3})\.coco\.json$", f).group(1))
            except Exception:
                continue

        # 获取所有raw_label下的子文件夹
        raw_label_subdirs = [
            d for d in os.listdir(raw_label_dir) if os.path.isdir(os.path.join(raw_label_dir, d))
        ]
        raw_label_versions = set(d.split(".")[-1] for d in raw_label_subdirs)

        # 检查是否有缺失的版本号
        for version in required_versions:
            version_num = version.split(".")[-1]
            if version_num not in coco_versions:
                errors.append(f"缺少版本 {version} 的 COCO 标签文件 {coco_versions}")
            if version_num not in raw_label_versions:
                errors.append(f"缺少版本 {version} 的 raw_label 文件夹")

        return errors

    @staticmethod
    def calculate_area(box):
        """计算多边形的面积"""
        polygon = Polygon(box)
        return polygon.area

    @staticmethod
    def iou(box1, box2):
        """计算两个边界框的交并比 (IoU)。"""
        # 创建多边形对象
        polygon1 = Polygon(box1)
        polygon2 = Polygon(box2)

        # 计算每个框的面积
        area1 = ValidateUtils.calculate_area(box1)
        area2 = ValidateUtils.calculate_area(box2)

        # 计算交集
        intersection_polygon = polygon1.intersection(polygon2)
        intersection_area = intersection_polygon.area
        # 计算 IoU
        return (
            intersection_area / float(area1 + area2 - intersection_area) if area1 + area2 > 0 else 0
        )

    @staticmethod
    def check_shapes_for_errors(shapes: list, json_filename: str) -> list:
        errors = []
        for i, shape in enumerate(shapes):
            points = shape.get("points", [])
            group_id = shape.get("group_id")

            # 如果为1框的正中间有一个点
            if group_id == 1:
                errors.append(f"文件 {json_filename}: 框 {i} 的 group_id 为 1")

            # 检查越界问题
            for point in points:
                if point[0] < 0 or point[1] < 0:
                    errors.append(f"文件 {json_filename}: 框 {i} 的 points 包含负值: {point}")
                elif point[0] > 1920 or point[1] > 1080:
                    errors.append(f"文件 {json_filename}: 框 {i} 的 points 大于1920*1080: {point}")

            # 检查重叠框
            for j in range(i + 1, len(shapes)):
                other_points = shapes[j].get("points", [])
                if ValidateUtils.iou(points, other_points) > 0.9:
                    errors.append(f"文件 {json_filename}: 框 {i} 与框 {j} 重叠 (IoU > 0.90)")

        return errors

    @staticmethod
    def check_for_spaces_in_paths(base_dir: str):
        errors = []
        for root, dirs, files in os.walk(base_dir):
            for name in dirs + files:
                if " " in name:
                    errors.append(f"名称中包含空格: '{os.path.join(root, name)}'")
        return errors

    @staticmethod
    def add_version_prefix(errors, version):
        """给错误列表中的每条错误加上版本号前缀."""
        return [f"[version {version}] {error}" for error in errors]

    @staticmethod
    def check_for_chinese_directory(base_dir: str):
        """检查 base_dir 目录中是否存在中文命名的文件夹"""
        errors = []
        chinese_directory_found = False

        # 判断是否是中文字符
        def is_chinese_char(uchar):
            if "\u4e00" <= uchar <= "\u9fa5":
                return True
            return False

        def has_chinese(string):
            return any(is_chinese_char(char) for char in string)

        # 遍历 base_dir 目录中的所有文件夹
        for entry in os.scandir(base_dir):
            if entry.is_dir() and has_chinese(entry.name):
                chinese_directory_found = True
                break

        if not chinese_directory_found:
            errors.append(f"{base_dir} 中不存在任何中文命名的文件夹")

        return errors

    @staticmethod
    def format_folder_name(dir_path: str, dir_name: str) -> None:
        """
        处理目标文件夹命名不规范的问题，如'v.20240531.r_001.s_01.i_001.img_cnt_{非七位位数字}.formal'，将其统一为7位数字
        """
        img_cnt = ValidateUtils.parse_img_cnt(dir_name)
        full_dir_path = os.path.join(dir_path, dir_name)
        if img_cnt is not None:
            new_img_cnt = ValidateUtils.format_img_cnt(img_cnt)
            new_dir_name = re.sub(r"\.img_cnt_\d+", f".img_cnt_{new_img_cnt}", dir_name)
            new_full_dir_path = os.path.join(dir_path, new_dir_name)
            if dir_name != new_dir_name:
                shutil.move(full_dir_path, new_full_dir_path)
                print(f"Renamed {full_dir_path} to {new_full_dir_path}")

    @staticmethod
    def parse_img_cnt(folder_name: str) -> int | None:
        """从文件夹或文件名称中提取 img_cnt 的值"""
        match = re.search(r"\.img_cnt_(\d+)", folder_name)
        if match:
            folder_content_count = int(match.group(1))
            assert (
                folder_content_count <= 9999999
            ), f"解析出来的图片数量大于9999999，请检查{folder_name}"
            return folder_content_count
        return None

    @staticmethod
    def format_img_cnt(img_cnt: int) -> str:
        """将 img_cnt 调整为七位字符串"""
        assert img_cnt <= 9999999, "img_cnt 大于 9999999"
        img_cnt_str = str(img_cnt)
        if len(img_cnt_str) > 7:
            # 多于七位，去掉一个开头的零
            while img_cnt_str.startswith("0") and len(img_cnt_str) > 7:
                img_cnt_str = img_cnt_str[1:]
        elif len(img_cnt_str) < 7:
            # 少于七位，补零
            img_cnt_str = img_cnt_str.zfill(7)
        return img_cnt_str

    @staticmethod
    def get_dir_latest_modification_time(dir_name: str) -> datetime | None:
        latest_time = None

        for root, dirs, files in os.walk(dir_name):
            for file in files:
                full_path = os.path.join(root, file)
                mod_time = os.path.getmtime(full_path)

                if latest_time is None or mod_time > latest_time:
                    latest_time = mod_time

        if latest_time is not None:
            return datetime.fromtimestamp(latest_time)
        return None

    # 检查非法的标签，如空字符串、空标签等
    @staticmethod
    def check_illegal_label_name(label_name: str) -> str | None:
        if (
            label_name is None
            or label_name.strip() == ""
            or " " in label_name
            # 标签必须为纯中文、数字与'、','-'的组合，并且'、-'不能出现在开头结尾，且不能连续出现
            or not (
                bool(re.match(r"^[\u4e00-\u9fa5、0-9-]+$", label_name))
                and "--" not in label_name
                and "、" * 2 not in label_name
                and label_name[0] not in "-、"
                and label_name[-1] not in "-、"
            )
        ):
            return f"标签名'{label_name}'非法！"
        return None

    @staticmethod
    def get_raw_label_json_file_list_giving_session_and_folder_list(
        base_dir: str,
        session_list: list[str],
        folder_list: list[str],
        formal: str = "formal.005",
    ) -> list[str]:
        result = []
        for session in session_list:
            session_path = os.path.join(base_dir, session)
            assert os.path.exists(session_path), f"session路径{session_path}不存在"
            for folder in os.listdir(session_path):
                if folder in folder_list:
                    raw_label_folder_path = os.path.join(session_path, folder, "raw_label", formal)
                    assert os.path.exists(
                        raw_label_folder_path
                    ), f"folder路径{raw_label_folder_path}不存在"
                    for file in os.listdir(raw_label_folder_path):
                        if file.endswith(".json"):
                            result.append(os.path.join(raw_label_folder_path, file))
        return result
