# 定义装饰器error_handler
from functools import wraps


def error_handler(logger):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录异常信息
                logger.error(f"Exception occurred: {e}", exc_info=True)
                return "error"

        return wrapper

    return decorator
