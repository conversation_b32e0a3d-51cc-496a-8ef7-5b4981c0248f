import concurrent.futures as futures
from tqdm import tqdm


def async_process(max_workers=5):
    """
    装饰器用于修饰函数，使其并发执行。只能用于单个目录列表。
    被修饰的方法第一个参数必须是process_dir，如run(process_dir, *args)
    Args:
        max_workers (int, optional): 并发度
    """

    def decorator(func):
        def wrapper(process_dir_list: list, *args):
            """
            Args:
                process_dir_list ：  需要处理的文件路径列表。
                args: 传递给worker的入参，从第二个入参开始。worker的第一个参数必须是process_dir，如run(process_dir, *args)
            """
            tasks = []
            results = []
            assert isinstance(
                process_dir_list, list
            ), f"async_process()修饰的方法{func}调用时第一个参数必须为需并发处理的列表, 实际入参是{type(process_dir_list)}"
            with futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                for process_dir in process_dir_list:
                    tasks.append(executor.submit(func, process_dir, *args))
                for task in tqdm(futures.as_completed(tasks), total=len(tasks), desc="处理进度"):
                    results.append(task.result())
            return results

        return wrapper

    return decorator
