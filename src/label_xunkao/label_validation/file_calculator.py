import fnmatch
import os
import re


class FileCalculator:
    """
    用于统计目录下每个文件夹的图片、json数量
    """

    def __init__(
        self,
        scan_dir: str,
        valid_session_pattern: str = None,
        image_folder_pattern: str = None,
    ):
        """
        Args:
            scan_dir: 扫描路径；需要是session的上级目录
            valid_session_pattern: 有效的session pattern，如session-1-v_[已审核]
            image_folder_pattern: 检查的文件夹pattern，如v.20240807.r_003.s_01.i_001.img_cnt_0000208.formal/
        """
        self.scan_dir = scan_dir
        self.valid_session_pattern = re.compile(
            valid_session_pattern if valid_session_pattern else r"^session-\d+-v"
        )
        self.image_folder_pattern = re.compile(
            image_folder_pattern
            if image_folder_pattern
            else r"^v\.\d{8}\.r_\d{3}\.s_\d{2}\.i_\d{3}\.img_cnt_\d{7}.(formal|demo)$"
        )
        self.report = []

    def get_label_results_folder_abs_path_list(self) -> list:
        label_results_folders = []

        for root, dirs, files in os.walk(self.scan_dir):
            for dir_name in dirs:
                if dir_name == "3.标注结果数据":
                    abs_path = os.path.abspath(os.path.join(root, dir_name))
                    label_results_folders.append(abs_path)

        return label_results_folders

    def get_valid_session_abs_path_list(self, label_results_folders: list[str]) -> list:
        valid_sessions = []

        for label_results_folder in label_results_folders:
            for root, dirs, files in os.walk(label_results_folder):
                for dir_name in dirs:
                    if self.valid_session_pattern.match(dir_name):
                        abs_path = os.path.abspath(os.path.join(root, dir_name))
                        valid_sessions.append(abs_path)

        return valid_sessions

    def get_image_folder_abs_path_list(self, valid_session_abs_path_list: list[str]) -> list:
        target_folders = []

        for session_abs_path in valid_session_abs_path_list:
            for root, dirs, files in os.walk(session_abs_path):
                for dir_name in dirs:
                    if self.image_folder_pattern.match(dir_name):
                        abs_path = os.path.abspath(os.path.join(root, dir_name))
                        target_folders.append(abs_path)

        return target_folders

    def get_image_count_by_folder_path(self, folder_abs_path: str) -> int:
        assert os.path.exists(folder_abs_path), f"{folder_abs_path} 文件夹目录不存在"
        image_path = os.path.join(folder_abs_path, "images")
        assert os.path.exists(image_path), f"{folder_abs_path} 路径下没有images文件夹，请检查"
        counter = 0
        for root, dirs, files in os.walk(image_path):
            for file in files:
                if fnmatch.fnmatch(file, "*.jpg") or fnmatch.fnmatch(file, "*.jpeg"):
                    counter += 1
        return counter

    def run(self):
        # 只扫描"3.标注结果数据"文件夹下的图片
        label_results_folders = self.get_label_results_folder_abs_path_list()
        valid_session_abs_path_list = self.get_valid_session_abs_path_list(label_results_folders)
        for i in self.get_image_folder_abs_path_list(valid_session_abs_path_list):
            folder_name = os.path.basename(i)
            self.report.append(
                {
                    "folder_name": folder_name,
                    "image_count": self.get_image_count_by_folder_path(i),
                }
            )

    def get_report(self) -> list:
        return self.report

    def print_report(self):
        for i in self.report:
            print(i)

    def save_report_to_excel(self, output_path: str = None):
        import pandas as pd

        df = pd.DataFrame(self.report)
        output_path = output_path if output_path else "output.xlsx"
        df.to_excel(output_path, index=False)
        print(f"数据已成功写入 {output_path}")


if __name__ == "__main__":
    scan_dir = "/mnt/2t_data/minio_data/aixunkao/1_考场异常行为分析"
    output_path = "output.xlsx"
    calculator = FileCalculator(scan_dir)
    calculator.run()
    calculator.print_report()
    calculator.save_report_to_excel(output_path)
