import os

import multiprocessing
from tqdm import tqdm
from functools import partial
from coco_validator import CocoValidator
from raw_label_validator import RawLabelValidator
from validate_utils.error_handler import error_handler
from validate_utils.logger import setup_logger
from validate_utils.validate_utils import ValidateUtils
import logging

logger = setup_logger("FileChecker")


class FileChecker:
    """
    用于检查COCO数据集的类，包括检查图片格式、检查标签文件名、检查标签文件格式等。
    """

    ROUND_NAME_LIST = [
        "round-1_20240531_第一轮数据采集-天府通用航空",
        "round-2_20240704_第二轮采集数据-七中万达通锦",
        "round-3_20240807_第三轮采集数据-成都工业职业技术学院",
        "round-4_20240816_第四轮采集数据-电子科技大学沙河校区",
        "round-5_20240920_第五轮采集数据-四川科技职业学院",
    ]

    LATEST_VERSION = "formal.005"  # 最新版本；如果为指定版本列表的话默认为最新版本

    def __init__(
        self,
        root_dir: str,
        round_num: int = None,
        processes_num: int = None,
        required_versions: list = None,
    ):
        """
        Args:
            root_dir[str]: 待检查的根目录路径,需要指定到ROUND_NAMES的上级目录
            round_num[int]:检查轮次，默认为全量检查
            processes_num[int]: 指定使用的CPU核心数，默认为None（使用CPU核心数的一半）
            required_versions[list]: 需要检查的版本, 默认为005
        """
        assert os.path.exists(root_dir), f"{root_dir}路径不存在"
        self.processes_num = (
            processes_num if processes_num else max(1, (multiprocessing.cpu_count() // 2) + 4)
        )
        round_path = [root_dir + "/" + ROUND_NAME for ROUND_NAME in self.ROUND_NAME_LIST]
        if round_num:
            assert round_num in range(
                1, len(self.ROUND_NAME_LIST) + 1
            ), f"输入的轮次不正确, 需要在1-{len(self.ROUND_NAME_LIST)}之间"

            round_abs_path_str = round_path[round_num - 1]
            assert os.path.exists(root_dir), f"{round_abs_path_str}路径不存在"
            self.to_be_checked_round_path_list = [round_abs_path_str]
        else:
            self.to_be_checked_round_path_list = round_path
            for round_path in self.to_be_checked_round_path_list:
                if not os.path.exists(round_path):
                    logging.error(f"{round_path}; 路径不存在")
                    print(f"{round_path}; 路径不存在")
        self.required_versions = required_versions if required_versions else [self.LATEST_VERSION]

    def run(self):
        # 收集所有需要处理的目录
        dirs_to_process = []

        for round_path in self.to_be_checked_round_path_list:
            for root, dirs, files in os.walk(round_path):
                for dir_name in dirs:
                    # 文件夹名符合规范如'v.20240531.r_001.s_01.i_001.img_cnt_0000253.formal'
                    # 或者'v.20240531.r_001.s_01.i_001.p_01.img_cnt_0000253.formal'

                    # 处理目标文件夹命名不规范的问题，如'v.20240531.r_001.s_01.i_001.img_cnt_{非七位数字数字}.formal'
                    ValidateUtils.format_folder_name(root, dir_name)
                    # TODO: demo后缀的文件夹未做校验
                    if ValidateUtils.validate_folder_name(dir_name):
                        full_sub_dir_path = os.path.join(root, dir_name)
                        # session名带-v 且排除扩充数据
                        if (
                            "-v" in full_sub_dir_path
                            and "扩充标注结果数据" not in full_sub_dir_path
                        ):
                            dirs_to_process.append(full_sub_dir_path)

        print(f"找到 {len(dirs_to_process)} 个目录需要处理")

        # 创建进程池
        results = self.register_pool_and_process(dirs_to_process)
        # 打印总结
        successful = sum(1 for _, status in results if status is True)
        failed = len(results) - successful
        print("\n处理完成！")
        print(f"总共处理: {len(results)} 个目录")
        print(f"成功: {successful} 个")
        print(f"失败或有错误: {failed} 个")

        # 打印失败的目录
        if failed > 0:
            print("\n以下目录存在问题：")
            for dir_path, status in results:
                if status is not True:
                    print(f"- {os.path.basename(dir_path)}")

    def register_pool_and_process(self, dirs_to_process: str) -> list:
        print(f"使用 {self.processes_num} 个进程进行处理")
        with multiprocessing.Pool(processes=self.processes_num) as pool:
            # 使用偏函数固定required_versions参数
            process_dir = partial(
                self.process_single_directory, required_versions=self.required_versions
            )

            # 使用tqdm创建进度条
            results = []
            for result in tqdm(
                pool.imap_unordered(process_dir, dirs_to_process),
                total=len(dirs_to_process),
                desc="处理进度",
            ):
                assert isinstance(result, tuple), "获取返回结果异常，请检查FileChecker.error.log"
                results.append(result)
        return results

    @error_handler(logger)
    def process_single_directory(self, base_dir, required_versions) -> tuple:
        """处理单个目录的函数;"""
        all_errors = []
        print(os.path.basename(base_dir))
        images_dir = os.path.join(base_dir, "images")
        coco_label_dir = os.path.join(base_dir, "coco_label")
        raw_label_dir = os.path.join(base_dir, "raw_label")
        all_errors = []

        """
            通用检查
        """
        # 检查 images 文件夹中的文件格式
        image_format_errors = ValidateUtils.check_images_format(images_dir)
        all_errors.extend(image_format_errors)

        # 检查是否存在中文命名的文件夹
        chinese_directory_errors = ValidateUtils.check_for_chinese_directory(base_dir)
        all_errors.extend(chinese_directory_errors)

        # 检查上级目录名称是否有空格
        space_in_parent_dir_errors_ = ValidateUtils.check_spaces_in_parent_dirs(base_dir)
        all_errors.extend(space_in_parent_dir_errors_)

        # 检查版本号完整性
        version_completeness_errors = ValidateUtils.check_versions_completeness(
            coco_label_dir, raw_label_dir, required_versions
        )
        all_errors.extend(version_completeness_errors)

        """
            coco检查
        """
        # 获取所有coco_label下的JSON文件
        coco_files = [
            os.path.join(coco_label_dir, f)
            for f in os.listdir(coco_label_dir)
            if f.endswith(".json")
        ]

        required_version_coco_files = []  # 只检查需要版本的coco.json
        for coco_file in coco_files:
            for version in required_versions:
                if version in coco_file:
                    required_version_coco_files.append(coco_file)

        # 检查每个coco_label JSON文件
        for coco_file in required_version_coco_files:
            coco_validator = CocoValidator(coco_file, images_dir, base_dir)
            all_errors.extend(coco_validator.run())

        """
            raw_label检查
        """
        raw_label_validator = RawLabelValidator(raw_label_dir, images_dir)
        all_errors.extend(raw_label_validator.run())

        # 创建并写入所有错误到文件
        output_file_name = os.path.basename(base_dir) + "_check_result.txt"
        file_path = os.path.join("coco_data_check/log/", output_file_name)

        if all_errors:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w", encoding="utf-8") as output_file:
                output_file.write("以下是一些发现的问题：\n")
                for error in all_errors:
                    output_file.write(error + "\n")
            return base_dir, False
        else:
            return base_dir, True


if __name__ == "__main__":
    root_dir = r"/mnt/2t_data/minio_data/aixunkao/1_考场异常行为分析"
    file_checker = FileChecker(root_dir=root_dir, round_num=1)
    file_checker.run()
