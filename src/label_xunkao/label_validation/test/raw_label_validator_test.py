import os
import unittest
from unittest.mock import patch
import sys

# 获取上级目录的绝对路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
# 导入上级目录中的模块
from raw_label_validator import RawLabelValidator


class TestRawLabelValidator(unittest.TestCase):

    @classmethod
    @patch("os.path.exists")
    def setUpClass(self, mock_exists):
        self.test_file_folder = "test_temp_files"
        os.makedirs(self.test_file_folder, exist_ok=True)

        self.formal_folder = os.path.join(self.test_file_folder, "formal.test")
        os.makedirs(self.formal_folder, exist_ok=True)

        with open(os.path.join(self.formal_folder, "not_a_json_file.txt"), "w") as f:
            f.write("123")

        components = [self.test_file_folder]
        # 使用 os.path.join 连接路径
        self.test_raw_label_dir = os.path.join(*components)
        self.test_images_dir = "mock_images_dir"
        mock_exists.return_value = True
        self.validator = RawLabelValidator(self.test_raw_label_dir, self.test_images_dir)

    @classmethod
    def tearDownClass(self):
        # 测试完成后删除创建的测试图片文件夹及其中的文件
        for root, dirs, files in os.walk(self.test_file_folder, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(self.test_file_folder)

    def test_check_raw_label_json_format(self):
        errors = self.validator.check_raw_label_json_format()
        expected_errors = ["'not_a_json_file.txt' 不是 json 文件，不应该在raw_label文件夹中"]
        self.assertEqual(errors, expected_errors)

    @patch("os.listdir")
    def test_verify_image_and_label_consistency(self, mock_listdir):

        # 定义 side_effect 函数
        def side_effect(path):
            if path == "mock_images_dir":
                return ["image1.jpg", "image2.png", "image3.jpg", "document.txt"]
            elif path == "test_temp_files":
                return ["formal.test"]
            else:
                return ["image1.json"]

        mock_listdir.side_effect = side_effect

        validator = RawLabelValidator(self.test_raw_label_dir, self.test_images_dir)
        errors = validator.verify_image_and_label_consistency()
        expected_errors = [
            "图片数量 (2) 与 JSON 文件数量 (1) 在 formal.test 文件夹中不一致",
            "在 formal.test 文件夹中找不到与这些图片前缀对应的 JSON 文件: {'image3'}",
        ]
        self.assertEqual(errors, expected_errors)

    @patch("os.listdir")
    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    @patch("validate_utils.validate_utils.ValidateUtils.check_shapes_for_errors")
    @patch("validate_utils.validate_utils.ValidateUtils.check_illegal_label_name")
    def test_check_shapes_errors(
        self,
        mock_check_illegal_label_name,
        mock_check_shapes_for_errors,
        mock_load_json_file,
        mock_listdir,
    ):
        mock_listdir.return_value = ["file1.json", "file2.json"]

        mock_load_json_file.side_effect = [
            {"shapes": [{"label": "valid_label", "points": [1, 1, 2, 2]}]},  # file1.json
            {"shapes": [{"label": "invalid_label", "points": [1, 1, 2, 2]}]},  # file2.json
        ]

        mock_check_shapes_for_errors.side_effect = [
            ["Shape error in file1"],  # file1.json
            ["Shape error in file2"],  # file2.json
        ]

        mock_check_illegal_label_name.side_effect = lambda label: (
            "Illegal label" if label == "invalid_label" else ""
        )

        errors = self.validator.check_shapes_errors()

        self.assertIn("Shape error in file1", errors)
        self.assertIn("Shape error in file2", errors)
        self.assertIn("文件 file2.json的shapes字段里的标签非法: Illegal label", errors)
        self.assertNotIn("文件 file1.json的shapes字段里的标签非法", errors)


if __name__ == "__main__":
    unittest.main()
