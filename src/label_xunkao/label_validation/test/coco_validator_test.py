from datetime import datetime, timedelta
import unittest
from unittest.mock import patch
import sys
import os

# 获取上级目录的绝对路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
# 导入上级目录中的模块
from coco_validator import CocoValidator


class TestCOCOValidator(unittest.TestCase):

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    def setUp(self, mock_load_json_file):
        mock_load_json_file.return_value = {}
        self.validator = CocoValidator("fake_coco_file.json", "fake_images_dir", "fake_base_dir")

        components = ["test", "测试用例", "损坏图片.jpg"]
        # 使用 os.path.join 连接路径
        self.damaged_image_path = os.path.join(*components)

    """
    单元测试
    """

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    @patch("os.listdir")
    @patch("os.path.exists")
    @patch("PIL.Image.open")
    def test_check_duplicate_image_names(
        self, mock_open, mock_exists, mock_listdir, mock_load_json_file
    ):
        mock_listdir.return_value = ["img1.jpg", "img2.jpg", "img3.jpg"]
        mock_exists.return_value = True
        # 测试结果mock；第一次返还不重复的列表；第二次返还有重复的列表
        mock_load_json_file.side_effect = [
            {
                "images": [
                    {"file_name": "img1.jpg"},
                    {"file_name": "img2.jpg"},
                    {"file_name": "img3.jpg"},
                ]
            },
            {
                "images": [
                    {"file_name": "img1.jpg"},
                    {"file_name": "img1.jpg"},
                ]
            },
        ]
        # case1： 无重复文件名
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        happy_path = coco_validator.check_duplicate_image_names()
        self.assertEqual(happy_path, [])
        # case2： 有重复文件名
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        sad_path = coco_validator.check_duplicate_image_names()
        self.assertTrue(len(sad_path) > 0)

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    @patch("os.listdir")
    @patch("os.path.exists")
    @patch("PIL.Image.open")
    def test_check_image_count_consistency(
        self, mock_open, mock_exists, mock_listdir, mock_load_json_file
    ):
        mock_listdir.return_value = ["img1.jpg", "img2.jpg", "img3.jpg"]
        mock_exists.return_value = True
        # 测试结果mock；第一次返还数量一致；第二次返还少一张图片，第三次返还多一张图片
        mock_load_json_file.side_effect = [
            {
                "images": [
                    {"file_name": "img1.jpg"},
                    {"file_name": "img2.jpg"},
                    {"file_name": "img3.jpg"},
                ]
            },
            {
                "images": [
                    {"file_name": "img1.jpg"},
                    {"file_name": "img2.jpg"},
                ]
            },
            {
                "images": [
                    {"file_name": "img1.jpg"},
                    {"file_name": "img2.jpg"},
                    {"file_name": "img3.jpg"},
                    {"file_name": "img4.jpg"},
                ]
            },
        ]
        # case1： 数量一致
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case1 = coco_validator.check_image_count_consistency()
        self.assertEqual(case1, [])

        # case2：少一张
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case2 = coco_validator.check_image_count_consistency()
        self.assertTrue(len(case2) > 0)

        # case3：多一张
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case3 = coco_validator.check_image_count_consistency()
        self.assertTrue(len(case3) > 0)

    def test_check_valid_coco_filename_format(self):
        valid_coco_names = [
            "v.20241001.r_001.s_01.i_001.img_cnt_0000001.formal.002.coco.json",
            "v.20241002.r_123.s_12.i_123.img_cnt_1234567.demo.001.coco.json",
            "v.20241003.r_999.s_99.i_999.img_cnt_9999999.formal.100.coco.json",
            "v.20241003.r_999.s_99.i_999.p_01.img_cnt_9999999.formal.100.coco.json",
            "v.20241001.r_999.s_99.i_999.img_cnt_9999999.demo.001.coco.json",
        ]
        for file_name in valid_coco_names:
            errors = CocoValidator.check_coco_filename_format(file_name)
            self.assertEqual(errors, [])

    def test_check_invalid_coco_label_file_name(self):
        invalid_coco_names = [
            "v.20231001.r_001.s_01.i_001.img_cnt_0000001.formal.00.coco.json",  # 少一位
            "v.20231002.r_123.s_12.i_123.img_cnt_1234567.demo1.001.coco.json",  # demo1不规范
            "v.20231003.r_999.s_99.i_999.img_cnt_9999999.forma.000.coco.json",  # forma不规范
            "v.20241001.r_999.s_99.i_999.img_cnt_9999999.demo.001.coco",  # 缺少后缀
            "20230101.r_123.s_45.i_678.img_cnt_1234567.coco.json",  # 缺少 v. 前缀
            "v.202301.r_123.s_45.i_678.img_cnt_1234567.coco.json",  # 时间部分不是 8 位数字
            "v.20230101.r_12.s_45.i_678.img_cnt_1234567.coco.json",  # r 后面不是 3 位数字
            "v.20230101.r_123.s_4.i_678.img_cnt_1234567.coco.json",  # s 后面不是 2 位数字
            "v.20230101.r_123.s_45.i_67.img_cnt_1234567.coco.json",  # i 后面不是 3 位数字
            "v.20230101.r_123.s_45.i_678.img_cnt_123456.coco.json",  # img_cnt_ 后面不是 7 位数字
            "v.20230101.s_45.i_678.img_cnt_1234567.coco.json",  # 缺少 r 部分
            "v.20230101.r_123.i_678.img_cnt_1234567.coco.json",  # 缺少 s 部分
            "v.20230101.r_123.s_45.img_cnt_1234567.coco.json",  # 缺少 i 部分
            "v.20230101.r_123.s_45.i_678.coco.json",  # 缺少 img_cnt_ 部分
            "v.20230101.r_123.s_45.i_678.img_cnt_1234567_extra.coco.json",  # 多余的部分
            "v.20230101.r_123.s_45.i_678.img_cnt_1234567!.coco.json",  # 特殊字符
            "v.20001232.r_999.s_99.i_999.img_cnt_9999999.coco.json",  # 日期不合法
            "v.20300101.r_999.s_99.i_999.img_cnt_9999999.coco.json",  # 未来日期
            "v.20241003.r_999.s_99.i_999.p01.img_cnt_9999999.formal.100.coco.json",  # 分片标志不合法
        ]
        for file_name in invalid_coco_names:
            errors = CocoValidator.check_coco_filename_format(file_name)
            self.assertTrue(len(errors) > 0)

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    @patch("os.listdir")
    @patch("os.path.exists")
    @patch("PIL.Image.open")
    def test_check_bounding_boxes_in_image(
        self, mock_open, mock_exists, mock_listdir, mock_load_json_file
    ):
        mock_listdir.return_value = ["img1.jpg", "img2.jpg", "img3.jpg"]
        mock_exists.return_value = True
        mock_load_json_file.side_effect = [
            {
                "images": [
                    {
                        "id": 1,
                        "file_name": "r_001.s_01.i_003_000000.jpg",
                        "width": 1920,
                        "height": 1080,
                        "license": 0,
                        "flickr_url": "",
                        "coco_url": "",
                        "date_captured": "",
                    }
                ],
                "annotations": [
                    {
                        "id": 1,
                        "image_id": 1,
                        "category_id": 17,
                        "bbox": [
                            68.53658536585371,
                            252.19512195121948,
                            128.0487804878049,
                            112.19512195121953,
                        ],
                        "area": 14366.44854253421,
                        "iscrowd": 0,
                        "ignore": 0,
                        "segmentation": [],
                    }
                ],
            },
            # x 为负
            {
                "images": [
                    {
                        "id": 2,
                        "file_name": "r_001.s_01.i_003_000000.jpg",
                        "width": 1920,
                        "height": 1080,
                        "license": 0,
                        "flickr_url": "",
                        "coco_url": "",
                        "date_captured": "",
                    }
                ],
                "annotations": [
                    {
                        "id": 2,
                        "image_id": 2,
                        "category_id": 17,
                        "bbox": [
                            -0.1,
                            252.19512195121948,
                            128.0487804878049,
                            112.19512195121953,
                        ],
                        "area": 14366.44854253421,
                        "iscrowd": 0,
                        "ignore": 0,
                        "segmentation": [],
                    }
                ],
            },
            # x + w > 1920
            {
                "images": [
                    {
                        "id": 3,
                        "file_name": "r_001.s_01.i_003_000000.jpg",
                        "width": 1920,
                        "height": 1080,
                        "license": 0,
                        "flickr_url": "",
                        "coco_url": "",
                        "date_captured": "",
                    }
                ],
                "annotations": [
                    {
                        "id": 3,
                        "image_id": 3,
                        "category_id": 17,
                        "bbox": [1920, 0.5, 128.0487804878049, 112.19512195121953],
                        "area": 14366.44854253421,
                        "iscrowd": 0,
                        "ignore": 0,
                        "segmentation": [],
                    }
                ],
            },
            # y + h > 1080
            {
                "images": [
                    {
                        "id": 4,
                        "file_name": "r_001.s_01.i_003_000000.jpg",
                        "width": 1920,
                        "height": 1080,
                        "license": 0,
                        "flickr_url": "",
                        "coco_url": "",
                        "date_captured": "",
                    }
                ],
                "annotations": [
                    {
                        "id": 4,
                        "image_id": 4,
                        "category_id": 17,
                        "bbox": [0.5, 1000, 128.0487804878049, 112.19512195121953],
                        "area": 14366.44854253421,
                        "iscrowd": 0,
                        "ignore": 0,
                        "segmentation": [],
                    }
                ],
            },
        ]
        # case1： 数量一致
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case1 = coco_validator.check_bounding_boxes_in_image()
        self.assertEqual(case1, [])

        # case2：x 为负
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case2 = coco_validator.check_bounding_boxes_in_image()
        self.assertTrue(len(case2) > 0)

        # case3：x + w > 1920
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case3 = coco_validator.check_bounding_boxes_in_image()
        self.assertTrue(len(case3) > 0)

        # case4：y + h > 1080
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case4 = coco_validator.check_bounding_boxes_in_image()
        self.assertTrue(len(case4) > 0)

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    @patch("os.listdir")
    @patch("os.path.exists")
    @patch("PIL.Image.open")
    def test_check_image_format_and_integrity(
        self, mock_open, mock_exists, mock_listdir, mock_load_json_file
    ):

        mock_listdir.return_value = ["img1.txt", "img2.jpg", "img3.jpg"]
        mock_exists.return_value = True
        mock_load_json_file.return_value = {
            "images": [
                {"file_name": "img1.txt"},
                {"file_name": "img2.jpg"},
                {"file_name": "img3.jpg"},
            ]
        }
        mock_image = mock_open.return_value
        mock_image_enter = mock_image.__enter__.return_value
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")

        # case1 : 格式不正确
        mock_image_enter.format = "txt"
        case1 = coco_validator.check_image_format_and_integrity()
        self.assertTrue(len(case1) > 0)

        # case2: 格式正确
        mock_image_enter.format = "JPG"
        case2 = coco_validator.check_image_format_and_integrity()
        self.assertTrue(len(case2) == 0)

        # case3: 格式正确但小写
        mock_image_enter.format = "jpeg"
        case3 = coco_validator.check_image_format_and_integrity()
        self.assertTrue(len(case3) == 0)

        # case4: 打开抛异常
        mock_image_enter.format = ValueError()
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        case4 = coco_validator.check_image_format_and_integrity()
        self.assertTrue(len(case4) > 0)

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    def test_check_cat_images_rlg_with_errors(self, mock_load_json_file):
        # 测试数据包含错误的情况
        self.validator.coco_data = {
            "categories": [
                {"name": "cat"},
                {"name": "dog"},
                {"name": "cat"},
            ],  # 重复的类别名称
            "images": [
                {"id": 1, "file_name": "img1.jpg"},
                {"id": 2, "file_name": "img2.jpg"},
                {"id": 3, "file_name": "img1.jpg"},  # 重复的文件名
            ],
            "annotations": [
                {
                    "image_id": 1,
                    "bbox": [10, 20, 30],
                    "ignore": 0,
                },  # bbox 不包含四个坐标
                {"image_id": 2, "bbox": [10, 20, 30, 40], "ignore": 1},  # ignore 不为 0
            ],
        }
        errors = self.validator.check_cat_images_rlg()
        self.assertTrue(len(errors) > 0)
        self.assertIn(
            "coco_label 文件 fake_coco_file.json 中的 categories 存在重复的名称: {'cat'}",
            errors,
        )
        self.assertIn(
            "coco_label 文件中的json文件存在重复的图片名: 'img1.jpg', 对应的 id: [1, 3]",
            errors,
        )
        self.assertIn("bbox 小于四个坐标, 图片名: 'img1.jpg'", errors)
        self.assertIn("ignore 字段不为 0, 图片名: 'img2.jpg'", errors)

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    def test_check_cat_images_rlg_successful(self, mock_load_json_file):
        # 测试数据包含错误的情况
        self.validator.coco_data = {
            "categories": [{"name": "有效标签一"}, {"name": "有效标签二"}],
            "images": [
                {"id": 1, "file_name": "img1.jpg"},
                {"id": 2, "file_name": "img2.jpg"},
            ],
            "annotations": [
                {"image_id": 1, "bbox": [10, 20, 30, 40], "ignore": 0},
                {"image_id": 2, "bbox": [10, 20, 30, 40], "ignore": 0},
            ],
        }
        errors = self.validator.check_cat_images_rlg()
        self.assertTrue(len(errors) == 0)

    def test_check_json_structure_valid(self):
        # 构造一个符合COCO格式的JSON结构
        self.validator.coco_data = {"images": [], "annotations": [], "categories": []}
        errors = self.validator.check_json_structure()
        self.assertEqual(errors, [], "应该没有错误，因为所有必需的键都存在")

    def test_check_json_structure_invalid_missing_key(self):
        # 构造一个缺少必要字段的JSON结构
        self.validator.coco_data = {"images": [], "annotations": []}
        errors = self.validator.check_json_structure()
        self.assertIn(
            "JSON 文件结构不符合 COCO 格式，缺少以下字段: {'categories'}",
            errors,
            "缺少'categories'键，应该返回相应的错误信息",
        )

    def test_check_json_structure_invalid_extra_key(self):
        # 构造一个包含额外字段的JSON结构，但仍然缺少必要字段
        self.validator.coco_data = {
            "images": [],
            "annotations": [],
            "categories": [],
            "extra_key": [],
        }
        errors = self.validator.check_json_structure()
        self.assertEqual(errors, [], "应该没有错误，因为所有必需的键都存在，即使有额外的键")

    def test_check_json_structure_invalid_empty(self):
        # 构造一个空的JSON结构
        self.validator.coco_data = {}
        errors = self.validator.check_json_structure()
        self.assertIn(
            "JSON 文件结构不符合 COCO 格式，缺少以下字段:",
            errors[0],
            "没有一个必需的键，应该返回相应的错误信息",
        )

    def test_check_category_consistency_with_errors(self):
        self.validator.coco_data = {
            "categories": [{"id": 1, "name": "person"}, {"id": 2, "name": "car"}],
            "annotations": [
                {"category_id": 1},
                {"category_id": 2},
                {"category_id": 3},  # 这个类别ID将导致错误
            ],
        }
        # 测试不一致的类别ID时的情况
        errors = self.validator.check_category_consistency()
        self.assertEqual(len(errors), 1)
        self.assertIn("标注文件中存在无效的类别ID:", errors[0])

    @patch("os.path.exists")
    def test_check_image_files_exist(self, mock_exists):
        mock_exists.side_effect = [True, False]

        self.validator.coco_data = {
            "images": [
                {"id": 1, "file_name": "img1.jpg"},
                {"id": 2, "file_name": "img2.jpg"},
            ]
        }
        errors = self.validator.check_image_files_exist()
        self.assertEqual(len(errors), 1)
        self.assertIn("标注文件中在目录中不存在的图片文件: ['img2.jpg']", errors)

    @patch("os.path.exists")
    @patch("PIL.Image.open")
    def test_check_image_size_consistency(self, mock_open, mock_exists):
        self.validator.coco_data = {
            "images": [
                {"file_name": "img1.jpg", "width": 100, "height": 200},
                {"file_name": "img2.jpg", "width": 150, "height": 250},
            ]
        }
        mock_exists.return_value = True

        mock_image = mock_open.return_value
        mock_image_enter = mock_image.__enter__.return_value
        mock_image_enter.size = (100, 200)

        errors = self.validator.check_image_size_consistency()
        self.assertEqual(len(errors), 1)
        self.assertIn("图片尺寸不一致的文件: [('img2.jpg', (150, 250), (100, 200))]", errors)

    @patch("validate_utils.validate_utils.ValidateUtils.get_dir_latest_modification_time")
    @patch("os.path.getmtime")
    def test_check_coco_file_generated_by_latest_raw_label_files(
        self, mock_getmtime, mock_get_dir_latest_mod_time
    ):
        # 测试COCO文件夹的修改时间晚于raw_label文件夹的情况
        mock_getmtime.return_value = datetime.now().timestamp()
        mock_get_dir_latest_mod_time.return_value = datetime.now() - timedelta(
            hours=5
        )  # 假设raw_label文件夹的修改时间是5h前

        errors = self.validator.check_coco_file_generated_by_latest_raw_label_files()
        self.assertEqual(errors, [])

        # 测试raw_label文件夹不存在的情况
        mock_get_dir_latest_mod_time.return_value = None
        errors = self.validator.check_coco_file_generated_by_latest_raw_label_files()
        self.assertIn("未找到raw_label文件夹，请检查路径是否正确", errors)

        # 测试COCO文件夹的修改时间早于raw_label文件夹的情况
        mock_get_dir_latest_mod_time.return_value = datetime.now()
        mock_getmtime.return_value = (
            datetime.now() - timedelta(hours=5)
        ).timestamp()  # 假设COCO文件夹的修改时间是5h前

        errors = self.validator.check_coco_file_generated_by_latest_raw_label_files()
        self.assertIn("coco_label文件夹的修改时间早于raw_label文件夹，请检查是否正确生成", errors)

    @patch("os.path.basename")
    def test_check_coco_filenames_matching(self, mock_basename):
        mock_basename.return_value = "base_name"
        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = [
                "base_name.000.coco.json",
                "base_name.001.coco.json",
            ]
            errors = self.validator.check_coco_filenames_same_as_base_dir()
            self.assertEqual(errors, [])

    @patch("os.path.basename")
    def test_check_coco_filenames_not_matching(self, mock_basename):
        mock_basename.return_value = "base_name"

        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = ["wrong_name.000.coco.json"]
            errors = self.validator.check_coco_filenames_same_as_base_dir()
            self.assertEqual(len(errors), 1)
            self.assertIn(
                "文件 wrong_name.000.coco.json 的前缀与文件夹名称 base_name 不匹配",
                errors,
            )

            mock_listdir.return_value = [
                "base_name.000.coco.json",
                "base_name.001.coco.jpg",
            ]
            errors = self.validator.check_coco_filenames_same_as_base_dir()
            self.assertEqual(len(errors), 1)
            self.assertIn(
                "文件 base_name.001.coco.jpg 为非法文件；只允许json文件在此路径下",
                errors,
            )

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    def test_check_iscrowd_field_with_no_errors(self, mock_load_json_file):
        mock_load_json_file.return_value = {
            "annotations": [
                {"iscrowd": 0, "other_field": "value"},
            ]
        }
        validator = CocoValidator("fake_coco_file.json", "fake_images_dir", "fake_base_dir")
        errors = validator.check_iscrowd_field()
        self.assertEqual(errors, [], "应该没有错误，但返回了: {}".format(errors))

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    def test_check_iscrowd_field_with_errors(self, mock_load_json_file):
        mock_load_json_file.return_value = {
            "annotations": [
                {"iscrowd": 0, "other_field": "value"},
                {"iscrowd": 1, "other_field": "value"},
            ]
        }
        validator = CocoValidator("fake_coco_file.json", "fake_images_dir", "fake_base_dir")
        errors = validator.check_iscrowd_field()
        self.assertIn(
            "文件中的 annotations 中存在 iscrowd 字段不为 0 ",
            errors,
            "应该有错误，但返回了: {}".format(errors),
        )

    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    @patch("os.listdir")
    @patch("os.path.exists")
    def test_check_image_file_names_valid(self, mock_exists, mock_listdir, mock_load_json_file):

        mock_listdir.return_value = [
            "r_006.s_02.i_044_000053.jpg",
            "r_006.s_02.i_044_000053.txt",
            "r_006.s_02.i_044_00053.jpg",
            "r_006.s_02.i_44_000053.jpg",
        ]
        mock_exists.return_value = True
        mock_load_json_file.return_value = {
            "images": [
                {"file_name": "r_006.s_02.i_044_000053.jpg"},
                {"file_name": "r_006.s_02.i_044_000053.txt"},
                {"file_name": "r_006.s_02.i_044_00053.jpg"},
                {"file_name": "r_006.s_02.i_44_000053.jpg"},
            ]
        }
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        errors = coco_validator.check_image_file_names_valid()
        self.assertEqual(len(errors), 3)
        self.assertEqual(
            errors[0],
            "图片r_006.s_02.i_044_000053.txt类型不正确，期待为jpg实际为：'txt'",
        )
        self.assertEqual(
            errors[1],
            "图片r_006.s_02.i_044_00053.jpg的i部分格式不正确，应为'i_XXX_XXXXXX'：'i_044_00053'",
        )
        self.assertEqual(
            errors[2],
            "图片r_006.s_02.i_44_000053.jpg的i部分格式不正确，应为'i_XXX_XXXXXX'：'i_44_000053'",
        )

    # @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    # @patch("os.path.exists")
    # def test_error_logger(self, mock_exists, mock_load_json_file):
    #     mock_exists.return_value = True
    #     mock_load_json_file.return_value = {"images": [{"id": 1, "file_name": "fake_image.jpg"}]}
    #     # 触发报错
    #     CocoValidator("fake_coco_file.json", "fake_images_dir").check_image_size_consistency()

    # 集成测试：
    @patch("validate_utils.validate_utils.ValidateUtils.load_json_file")
    @patch("os.listdir")
    @patch("os.path.join")
    def test_real_image_check_image_format_and_integrity(
        self, mock_path_join, mock_listdir, mock_load_json_file
    ):
        mock_listdir.return_value = ["损坏图片.jpg"]
        mock_path_join.return_value = self.damaged_image_path
        mock_load_json_file.return_value = {
            "images": [
                {"file_name": "损坏图片.jpg"},
            ]
        }
        coco_validator = CocoValidator("fake_coco_file.json", "fake_images_dir")
        result = coco_validator.check_image_format_and_integrity()
        self.assertTrue(len(result) > 0)


if __name__ == "__main__":
    unittest.main()
