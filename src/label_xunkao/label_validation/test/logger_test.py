import logging
import os
import sys
import unittest

# 获取上级目录的绝对路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from validate_utils.logger import setup_logger


class TestSetupLogger(unittest.TestCase):
    def test_setup_logger(self):
        logger_name = "test_logger"
        logger = setup_logger(logger_name)

        # 检查logger是否被正确创建
        self.assertIsNotNone(logger)
        self.assertEqual(logger.name, logger_name)
        self.assertEqual(logger.level, logging.INFO)

        # 检查handler是否被正确添加
        has_handler = False
        for handler in logger.handlers:
            if isinstance(handler, logging.FileHandler) and handler.baseFilename.endswith(
                f"{logger_name}.error.log"
            ):
                has_handler = True
                self.assertTrue(handler.delay)
                # 检查格式化器是否被正确设置
                formatter = handler.formatter
                self.assertIsNotNone(formatter)
                self.assertEqual(
                    formatter._fmt,
                    "%(levelname)s::%(name)s::%(asctime)s::%(filename)s,line %(lineno)s, in %(funcName)s(),msg:%(message)s",
                )
        self.assertTrue(has_handler)

        # 清理生成的日志文件
        if os.path.exists(f"{logger_name}.error.log"):
            os.remove(f"{logger_name}.error.log")


if __name__ == "__main__":
    unittest.main()
