import unittest
import os
import sys
from unittest.mock import MagicMock

# 获取上级目录的绝对路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from validate_utils.error_handler import error_handler


logger = MagicMock()


# 定义一个使用装饰器的示例函数
@error_handler(logger)
def test_function(x):
    return 10 / x


class TestErrorHandler(unittest.TestCase):

    def test_normal_case(self):
        result = test_function(2)
        self.assertEqual(result, 5)

    def test_exception_case(self):
        result = test_function(0)
        self.assertEqual(result, "error")


# 运行测试
if __name__ == "__main__":
    unittest.main()
