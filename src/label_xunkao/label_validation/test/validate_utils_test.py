from datetime import datetime
import json
import os
import sys
import unittest
from unittest.mock import patch

# 获取上级目录的绝对路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
from validate_utils.validate_utils import ValidateUtils


class TestValidateUtil(unittest.TestCase):
    @classmethod
    def setUpClass(self):
        # 创建一些用于测试的空图片文件及对应的路径
        self.test_file_folder = "test_temp_files"
        os.makedirs(self.test_file_folder, exist_ok=True)

        # 创建待格式化的目录
        self.original_dir_name = "v.20240531.r_001.s_01.i_001.img_cnt_123456.formal"
        self.original_full_path = os.path.join(self.test_file_folder, self.original_dir_name)
        os.makedirs(self.original_full_path, exist_ok=True)

        # 创建含空格与不含空格的目录
        self.file_with_space = os.path.join(self.test_file_folder, "file with space.txt")
        self.file_without_space = os.path.join(self.test_file_folder, "filewithoutpace.txt")
        self.dir_with_space = os.path.join(self.test_file_folder, "dir with space")
        self.dir_without_space = os.path.join(self.test_file_folder, "dirwithoutpace")

        open(self.file_with_space, "a").close()
        open(self.file_without_space, "a").close()
        os.makedirs(self.dir_with_space, exist_ok=True)
        os.makedirs(self.dir_without_space, exist_ok=True)

        # 添加新的测试文件夹结构用于 get_raw_label_json_file_list_giving_session_and_folder_list 测试
        self.session_list = ["session_1"]
        self.folder_list = ["folder_1", "folder_2"]
        self.formal = "formal.005"

        # 创建会话和文件夹结构
        for session in self.session_list:
            session_path = os.path.join(self.test_file_folder, session)
            os.makedirs(session_path, exist_ok=True)
            for folder in self.folder_list:
                raw_label_folder_path = os.path.join(session_path, folder, "raw_label", self.formal)
                os.makedirs(raw_label_folder_path, exist_ok=True)
                # 创建一些测试 .json 文件
                for i in range(1, 4):
                    with open(
                        os.path.join(raw_label_folder_path, f"test_label_{i}.json"), "w"
                    ) as f:
                        f.write(json.dumps({"key": "value"}))

        self.file_times = []
        # 创建用于修改时间的测试文件；该步骤必须是最后
        for i in range(1, 6):
            with open(os.path.join(self.test_file_folder, f"text_{i}.txt"), "w") as f:
                f.write("123")
            # 模拟文件的修改时间
            mod_time = datetime.now()
            os.utime(
                os.path.join(self.test_file_folder, f"text_{i}.txt"),
                times=(mod_time.timestamp(), mod_time.timestamp()),
            )
            self.file_times.append(mod_time)

    @classmethod
    def tearDownClass(self):
        # 测试完成后删除创建的测试图片文件夹及其中的文件
        for root, dirs, files in os.walk(self.test_file_folder, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(self.test_file_folder)
        self.file_times = []

    def test_valid_prefix_folder_name(self):
        valid_prefix_folder_names = [
            "v.20231001.r_001.s_01.i_001.img_cnt_0000001",
            "v.20231002.r_123.s_12.i_123.img_cnt_1234567",
            "v.20231003.r_999.s_99.i_999.img_cnt_9999999",
            "v.20241001.r_999.s_99.i_999.img_cnt_9999999",
            "v.20231001.r_001.s_01.i_001.p_02.img_cnt_0000001",
        ]
        for folder_name in valid_prefix_folder_names:
            self.assertTrue(
                ValidateUtils.validate_prefix_folder_name(folder_name),
                f"Failed on {folder_name}",
            )

    def test_invalid_prefix_folder_name(self):
        invalid_prefix_folder_names = [
            "20230101.r_123.s_45.i_678.img_cnt_1234567",  # 缺少 v. 前缀
            "v.202301.r_123.s_45.i_678.img_cnt_1234567",  # 时间部分不是 8 位数字
            "v.20230101.r_12.s_45.i_678.img_cnt_1234567",  # r 后面不是 3 位数字
            "v.20230101.r_123.s_4.i_678.img_cnt_1234567",  # s 后面不是 2 位数字
            "v.20230101.r_123.s_45.i_67.img_cnt_1234567",  # i 后面不是 3 位数字
            "v.20230101.r_123.s_45.i_678.img_cnt_123456",  # img_cnt_ 后面不是 7 位数字
            "v.20230101.s_45.i_678.img_cnt_1234567",  # 缺少 r 部分
            "v.20230101.r_123.i_678.img_cnt_1234567",  # 缺少 s 部分
            "v.20230101.r_123.s_45.img_cnt_1234567",  # 缺少 i 部分
            "v.20230101.r_123.s_45.i_678",  # 缺少 img_cnt_ 部分
            "v.20230101.r_123.s_45.i_678.img_cnt_1234567_extra",  # 多余的部分
            "v.20230101.r_123.s_45.i_678.img_cnt_1234567!",  # 特殊字符
            "v.20241001.r_999.s_99.i_999.img_cnt_9999999.jpg",  # 不应该出现后缀的部分
            "v.20001232.r_999.s_99.i_999.img_cnt_9999999",  # 日期不合法
            "v.20300101.r_999.s_99.i_999.img_cnt_9999999",  # 未来日期
            "v.20300101.r_999.s_99.i_999.p01.img_cnt_9999999",  # 分片标志不合法
        ]
        for folder_name in invalid_prefix_folder_names:
            self.assertFalse(
                ValidateUtils.validate_prefix_folder_name(folder_name),
                f"Failed on {folder_name}",
            )

    def test_valid_folder_name(self):
        valid_folder_names = [
            "v.20231001.r_001.s_01.i_001.img_cnt_0000001.formal",
            "v.20231002.r_123.s_12.i_123.img_cnt_1234567.formal",
            "v.20231003.r_999.s_99.i_999.img_cnt_9999999.formal",
            "v.20241001.r_999.s_99.i_999.img_cnt_9999999.formal",
            "v.20241001.r_999.s_99.i_999.p_01.img_cnt_9999999.formal",
        ]
        for folder_name in valid_folder_names:
            self.assertTrue(
                ValidateUtils.validate_folder_name(folder_name),
                f"Failed on {folder_name}",
            )

    def test_invalid_folder_name(self):
        invalid_folder_names = [
            "v.20231001.r_001.s_01.i_001.img_cnt_0000001",  # 缺少后缀
            "v.20241001.r_999.s_99.i_999.img_cnt_9999999.jpg",  # 错误后缀
            "v.20001232.r_999.s_99.i_999.img_cnt_9999999.formal",  # 日期不合法
            "v.20300101.r_999.s_99.i_999.img_cnt_9999999.formal",  # 未来日期
            "v.20241001.r_999.s_99.i_999.p01.img_cnt_9999999.formal",
        ]
        for folder_name in invalid_folder_names:
            self.assertFalse(
                ValidateUtils.validate_folder_name(folder_name),
                f"Failed on {folder_name}",
            )

    def test_load_json_file_not_exist(self):
        with self.assertRaises(AssertionError):
            ValidateUtils.load_json_file("fake_coco_file.json")

    @patch("os.path.exists")
    def test_load_json_file_open_failed(self, mock_exist):
        mock_exist.return_value = True
        with self.assertRaises(Exception):
            ValidateUtils.load_json_file("fake_coco_file.json")

    # def test_extract_image_count_from_path(self):
    #     txt_pattern = r"text_(\d+)"
    #     img_pattern = r"img_(\d+)"
    #     path = "/test_temp_files"

    #     self.assertEqual(ValidateUtils.extract_image_count_from_path(path, txt_pattern), 6)
    #     self.assertEqual(ValidateUtils.extract_image_count_from_path(path, img_pattern), 0)

    def test_format_img_cnt(self):
        # 测试格式化 img_cnt 的功能
        self.assertEqual(ValidateUtils.format_img_cnt(123), "0000123")
        self.assertEqual(ValidateUtils.format_img_cnt(1234567), "1234567")
        self.assertEqual(ValidateUtils.format_img_cnt(0), "0000000")

    def test_parse_img_cnt_valid(self):
        # 测试有效的文件夹名称
        self.assertEqual(ValidateUtils.parse_img_cnt("folder.img_cnt_12345"), 12345)
        self.assertEqual(ValidateUtils.parse_img_cnt("folder.img_cnt_9999999"), 9999999)

    def test_parse_img_cnt_invalid(self):
        # 测试无效的文件夹名称
        self.assertIsNone(ValidateUtils.parse_img_cnt("folder"))
        self.assertIsNone(ValidateUtils.parse_img_cnt("folder.img_cnt_"))
        self.assertIsNone(ValidateUtils.parse_img_cnt("folder.img_cnt_abc"))

    def test_parse_img_cnt_assertion(self):
        # 测试超出范围的值是否引发断言错误
        with self.assertRaises(AssertionError):
            ValidateUtils.parse_img_cnt("folder.img_cnt_10000001")

    def test_format_folder_name(self):
        # 测试文件夹名称的格式化功能
        expected_new_dir_name = "v.20240531.r_001.s_01.i_001.img_cnt_0123456.formal"
        expected_new_full_path = os.path.join(self.test_file_folder, expected_new_dir_name)

        ValidateUtils.format_folder_name(self.test_file_folder, self.original_dir_name)

        # 检查是否重命名成功
        self.assertFalse(os.path.exists(self.original_full_path))
        self.assertTrue(os.path.exists(expected_new_full_path))

    def test_shapes_group_id_error(self):
        shapes = [{"group_id": 1, "points": [(10, 10), (20, 20)]}]
        json_filename = "test_shapes.json"
        errors = ValidateUtils.check_shapes_for_errors(shapes, json_filename)
        self.assertIn(f"文件 {json_filename}: 框 0 的 group_id 为 1", errors)

    def test_shapes_negative_points_error(self):
        shapes = [{"group_id": 2, "points": [(-10, 10), (20, 20)]}]
        json_filename = "test_shapes.json"
        errors = ValidateUtils.check_shapes_for_errors(shapes, json_filename)
        self.assertIn(f"文件 {json_filename}: 框 0 的 points 包含负值: (-10, 10)", errors)

    def test_shapes_out_of_bound_points_error(self):
        shapes = [{"group_id": 2, "points": [(1920.001, 1000), (20, 20)]}]
        json_filename = "test_shapes.json"
        errors = ValidateUtils.check_shapes_for_errors(shapes, json_filename)
        self.assertIn(
            f"文件 {json_filename}: 框 0 的 points 大于1920*1080: (1920.001, 1000)",
            errors,
        )

    def test_shapes_overlap_error(self):
        shapes = [
            {
                "group_id": 2,
                "points": [
                    [969.7560975609755, 52.19512195121952],
                    [1060.0, 52.19512195121952],
                    [1060.0, 136.34146341463415],
                    [969.7560975609755, 136.34146341463415],
                ],
            },
            {
                "group_id": 2,
                "points": [
                    [969.7560975609755, 52.29512195121952],
                    [1060.0, 52.29512195121952],
                    [1060.0, 136.44146341463415],
                    [969.7560975609755, 136.44146341463415],
                ],
            },
        ]
        json_filename = "test_shapes.json"
        errors = ValidateUtils.check_shapes_for_errors(shapes, json_filename)
        self.assertIn(f"文件 {json_filename}: 框 0 与框 1 重叠 (IoU > 0.90)", errors)

    def test_shapes_no_errors(self):
        shapes = [
            {
                "group_id": 2,
                "points": [
                    [969.7560975609755, 52.19512195121952],
                    [1060.0, 52.19512195121952],
                    [1060.0, 136.34146341463415],
                    [969.7560975609755, 136.34146341463415],
                ],
            },
            {
                "group_id": 2,
                "points": [
                    [770.9756097560975, 120.48780487804879],
                    [867.3170731707316, 120.48780487804879],
                    [867.3170731707316, 229.02439024390245],
                    [770.9756097560975, 229.02439024390245],
                ],
            },
        ]
        json_filename = "test_shapes.json"
        errors = ValidateUtils.check_shapes_for_errors(shapes, json_filename)
        self.assertEqual(errors, [])

    def test_with_spaces(self):
        # 测试包含空格的文件和目录
        errors = ValidateUtils.check_for_spaces_in_paths(self.test_file_folder)
        self.assertIn("名称中包含空格: 'test_temp_files", errors[0])
        self.assertIn("名称中包含空格: 'test_temp_files", errors[1])

    def test_without_spaces(self):
        # 测试不包含空格的文件和目录
        errors = ValidateUtils.check_for_spaces_in_paths(self.dir_without_space)
        self.assertEqual(errors, [])

    def test_get_dir_latest_modification_time(self):
        # 测试正常情况
        latest_time = ValidateUtils.get_dir_latest_modification_time(self.test_file_folder)
        self.assertIsNotNone(latest_time)
        self.assertEqual(latest_time, self.file_times[4])

    def test_get_dir_latest_modification_time_empty_dir(self):
        # 测试空目录情况
        empty_dir = "empty_dir"
        os.makedirs(empty_dir, exist_ok=True)
        latest_time = ValidateUtils.get_dir_latest_modification_time(empty_dir)
        self.assertIsNone(latest_time)
        os.rmdir(empty_dir)

    def test_with_chinese_directory(self):
        chinese_dir = os.path.join(self.test_file_folder, "中文文件夹")
        os.makedirs(chinese_dir, exist_ok=True)

        errors = ValidateUtils.check_for_chinese_directory(self.test_file_folder)
        self.assertEqual(
            errors,
            [],
            "Should not return any errors when a Chinese directory is present.",
        )

    def test_without_chinese_directory(self):
        english_dir = os.path.join(self.test_file_folder, "english_dir")
        os.makedirs(english_dir, exist_ok=True)

        errors = ValidateUtils.check_for_chinese_directory(english_dir)
        self.assertEqual(
            errors,
            [f"{english_dir} 中不存在任何中文命名的文件夹"],
            "Should return an error when no Chinese directory is present.",
        )

    @patch("os.listdir")
    @patch("os.path.isdir")
    def test_check_versions_completeness(self, mock_isdir, mock_listdir):
        # 模拟 os.listdir 的返回值
        mock_listdir.side_effect = [
            ["v.001.coco.json", "v.002.coco.json"],  # coco_label_dir
            ["v.001", "v.003"],  # raw_label_dir
        ]

        # 模拟 os.path.isdir 的返回值
        mock_isdir.side_effect = [True, True, True, True]

        # 定义测试数据
        coco_label_dir = "path/to/coco_label"
        raw_label_dir = "path/to/raw_label"
        required_versions = ["v.001", "v.002", "v.003"]

        # 调用方法并获取结果
        errors = ValidateUtils.check_versions_completeness(
            coco_label_dir, raw_label_dir, required_versions
        )

        # 断言结果
        expected_errors = [
            "缺少版本 v.002 的 raw_label 文件夹",
            "缺少版本 v.003 的 COCO 标签文件",
        ]
        self.assertEqual(errors[0], expected_errors[0])
        self.assertIn(expected_errors[1], errors[1])

        mock_listdir.side_effect = [
            ["v.001.coco.json"],  # coco_label_dir
            ["v.001"],  # raw_label_dir
        ]

        # 模拟 os.path.isdir 的返回值
        mock_isdir.side_effect = [True, True, True, True]

        # 定义测试数据
        coco_label_dir = "path/to/coco_label"
        raw_label_dir = "path/to/raw_label"
        required_versions = ["v.001"]

        # 调用方法并获取结果
        errors = ValidateUtils.check_versions_completeness(
            coco_label_dir, raw_label_dir, required_versions
        )

        self.assertEqual(errors, [])

    def test_check_illegal_label_name_invalid(self):
        self.assertEqual(ValidateUtils.check_illegal_label_name(None), "标签名'None'非法！")
        self.assertEqual(ValidateUtils.check_illegal_label_name(""), "标签名''非法！")
        self.assertEqual(ValidateUtils.check_illegal_label_name("   "), "标签名'   '非法！")
        self.assertEqual(
            ValidateUtils.check_illegal_label_name("中文标签带English"),
            "标签名'中文标签带English'非法！",
        )
        self.assertEqual(
            ValidateUtils.check_illegal_label_name("中文标签带空格 "),
            "标签名'中文标签带空格 '非法！",
        )
        self.assertEqual(
            ValidateUtils.check_illegal_label_name("中文--双横线"),
            "标签名'中文--双横线'非法！",
        )
        self.assertEqual(
            ValidateUtils.check_illegal_label_name("中文尾部顿号、"),
            "标签名'中文尾部顿号、'非法！",
        )

    def test_check_illegal_label_name_valid(self):
        result = ValidateUtils.check_illegal_label_name("有效标签")
        result = ValidateUtils.check_illegal_label_name("销毁试卷、答案")
        result = ValidateUtils.check_illegal_label_name("考生群体停止作答-提笔不写字")
        result = ValidateUtils.check_illegal_label_name("手势-左手-4")
        self.assertIsNone(result)

    def test_get_raw_label_json_file_list_giving_session_and_folder_list(self):
        session_list = ["session_1"]
        folder_list = ["folder_1"]
        expected_file_list = [
            os.path.join(
                self.test_file_folder,
                "session_1",
                "folder_1",
                "raw_label",
                "formal.005",
                "test_label_1.json",
            ),
            os.path.join(
                self.test_file_folder,
                "session_1",
                "folder_1",
                "raw_label",
                "formal.005",
                "test_label_2.json",
            ),
            os.path.join(
                self.test_file_folder,
                "session_1",
                "folder_1",
                "raw_label",
                "formal.005",
                "test_label_3.json",
            ),
        ]

        file_list = ValidateUtils.get_raw_label_json_file_list_giving_session_and_folder_list(
            self.test_file_folder, session_list, folder_list
        )
        assert len(file_list) == 3, "应该找到3个json文件"
        assert all(file in expected_file_list for file in file_list), "找到的文件列表不正确"

    """
        集成测试
    """

    def test_real_json_shapes_overlapping(self):
        components = ["test", "测试用例", "重复框.json"]
        # 使用 os.path.join 连接路径
        file_path = os.path.join(*components)
        raw_data = ValidateUtils.load_json_file(file_path)
        shapes = raw_data.get("shapes", [])
        errors = ValidateUtils.check_shapes_for_errors(shapes, "test_path")
        self.assertIn("文件 test_path: 框 0 与框 1 重叠 (IoU > 0.90)", errors)
        self.assertIn("文件 test_path: 框 1 与框 2 重叠 (IoU > 0.90)", errors)
        self.assertIn("文件 test_path: 框 0 与框 2 重叠 (IoU > 0.90)", errors)


if __name__ == "__main__":
    unittest.main()
