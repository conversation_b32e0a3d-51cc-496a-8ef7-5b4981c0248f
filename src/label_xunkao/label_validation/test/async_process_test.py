import unittest
import sys
import os

# 获取上级目录的绝对路径
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(parent_dir)
# 导入上级目录中的模块
from validate_utils.async_process import async_process


# 测试用例
class TestAsyncProcess(unittest.TestCase):

    # 测试异步处理装饰器的基本功能
    def test_async_process(self):
        # 创建一个模拟函数来测试异步处理
        def mock_function(process_dir, *args):
            return f"Processed {process_dir} with args {args}"

        # 创建异步处理装饰器实例
        @async_process(max_workers=3)
        def async_mock_function(process_dir, *args):
            return mock_function(process_dir, *args)

        # 测试数据
        process_dir_list = [f"dir_{i}" for i in range(10)]
        test_args = ("arg1", "arg2")

        # 调用被装饰的函数
        results = async_mock_function(process_dir_list, *test_args)

        # 确保所有任务都已完成
        self.assertEqual(len(results), len(process_dir_list))

        # 检查每个结果是否符合预期
        for i in range(len(results)):
            expected_result = mock_function(process_dir_list[i], *test_args)
            self.assertIn(expected_result, results)

    # 测试异步处理装饰器的异常处理
    def test_async_process_exception(self):
        # 创建一个模拟函数来测试异步处理中的异常
        def mock_function_with_exception(process_dir, *args):
            raise ValueError(f"Error processing {process_dir}")

        # 创建异步处理装饰器实例
        @async_process(max_workers=3)
        def async_mock_function(process_dir, *args):
            return mock_function_with_exception(process_dir, *args)

        # 测试数据
        process_dir_list = [f"dir_{i}" for i in range(5)]
        test_args = ("arg1", "arg2")

        # 调用被装饰的函数并检查异常
        with self.assertRaises(ValueError):
            async_mock_function(process_dir_list, *test_args)


# 主程序入口
if __name__ == "__main__":
    unittest.main()
