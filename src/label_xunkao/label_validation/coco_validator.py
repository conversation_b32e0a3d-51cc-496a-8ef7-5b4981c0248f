from datetime import datetime, timedelta
import os
import re
from PIL import Image
from validate_utils.validate_utils import ValidateUtils


class CocoValidator:

    def __init__(self, coco_file_path: str, images_dir: str = None, base_dir: str = None):
        """
        Args:
            coco_file_path: coco.json文件的路径,如/path/to/coco.json
            images_dir: 对应的images文件夹路径, 如/v.20240807.r_003.s_01.i_001.img_cnt_0000208.formal/images/
            base_dir: 对应的根目录名称，如如/v.20240807.r_003.s_01.i_001.img_cnt_0000208.formal/
        """
        self.coco_file_path = coco_file_path
        self.coco_data = ValidateUtils.load_json_file(coco_file_path)
        self.images_dir = images_dir
        self.base_dir = base_dir

    # 1. 检查图片文件名是否有重复
    def check_duplicate_image_names(self) -> list:
        errors = []
        image_names = [img["file_name"] for img in self.coco_data["images"]]
        duplicates = set([name for name in image_names if image_names.count(name) > 1])
        if duplicates:
            errors.append(f"重复的图片文件名: {duplicates}")
        return errors

    # 2. 检查标注文件中的图片总数是否与实际图片文件一致
    def check_image_count_consistency(self) -> list:
        errors = []
        coco_file_name = os.path.basename(self.coco_file_path)
        parts = coco_file_name.split(".")
        last_img_cnt = [p for p in parts if p.startswith("img_cnt_")][-1]
        coco_file_name_img_count = int(last_img_cnt.split("_")[-1])
        actual_images = set(os.listdir(self.images_dir))
        labeled_images = set([img["file_name"] for img in self.coco_data["images"]])
        if actual_images != labeled_images:
            missing_images = labeled_images - actual_images
            extra_images = actual_images - labeled_images
            if missing_images:
                errors.append(f"标注文件中的图片在目录中缺失: {missing_images}")
            if extra_images:
                errors.append(f"目录中的图片在标注文件中缺失: {extra_images}")
        if coco_file_name_img_count != len(labeled_images):
            errors.append(
                f"文件名{coco_file_name}中的数字{coco_file_name_img_count}和COCO文件中的图片数{len(labeled_images)}不符合"
            )
        # else:
        #     errors.append("标注文件与实际图片文件总数一致。")
        return errors

    # 3. 检查标注文件中的图片在实际文件中是否存在
    def check_image_files_exist(self) -> list:
        errors = []
        image_files = [img["file_name"] for img in self.coco_data["images"]]
        missing_files = [
            file for file in image_files if not os.path.exists(os.path.join(self.images_dir, file))
        ]
        if missing_files:
            errors.append(f"标注文件中在目录中不存在的图片文件: {missing_files}")
        # else:
        #     errors.append("所有标注文件中的图片文件在目录中都存在。")
        return errors

    # 15. 检查图片名称符合规范
    def check_image_file_names_valid(self) -> list:
        errors = []
        image_files = [img["file_name"] for img in self.coco_data["images"]]

        for image_file in image_files:
            # 用 "." 分割文件名
            parts = image_file.split(".")

            if len(parts) < 4:
                errors.append(
                    f"图片{image_file}的名称部分数量不正确，期望4个或者5个部分 parts {parts}"
                )
                return errors

            if len(parts) == 4:
                # 分别提取各部分
                (r_part, s_part, i_part, jpg_part) = parts
            else:
                (r_part, s_part, i_part) = parts[:3]
                jpg_part = parts[-1]

            # 校验 (r_001 格式，数字 3 位，范围 1-999)
            if not re.match(r"^r_(\d{3})$", r_part):
                errors.append(
                    f"图片{image_file}的r部分格式不正确，应为'r_XXX'，其中XXX为3位数字：'{r_part}'"
                )
            elif not (1 <= int(r_part[2:]) <= 999):
                errors.append(f"图片{image_file}的r部分数值范围错误，应在1-999之间：'{r_part}'")

            # 校验 (s_02 格式，数字 2 位，范围 1-99)
            if not re.match(r"^s_(\d{2})$", s_part):
                errors.append(
                    f"图片{image_file}的s部分格式不正确，应为's_XX'，其中XX为2位数字：'{s_part}'"
                )
            elif not (1 <= int(s_part[2:]) <= 99):
                errors.append(f"图片{image_file}的s部分数值范围错误，应在1-99之间：'{s_part}'")

            if not re.match(r"^i_\d{3}_\d{6}$", i_part):
                errors.append(f"图片{image_file}的i部分格式不正确，应为'i_XXX_XXXXXX'：'{i_part}'")

            # 校验(jpg 固定后缀)
            if jpg_part != "jpg":
                errors.append(f"图片{image_file}类型不正确，期待为jpg实际为：'{jpg_part}'")
        return errors

    # 4. 检查每个图片标注的尺寸是否与实际图片文件的尺寸一致
    def check_image_size_consistency(self) -> list:
        errors = []
        inconsistent_sizes = []
        for img in self.coco_data["images"]:
            img_path = os.path.join(self.images_dir, img["file_name"])
            if os.path.exists(img_path):
                with Image.open(img_path) as image:
                    width, height = image.size
                    if img["width"] != width or img["height"] != height:
                        inconsistent_sizes.append(
                            (
                                img["file_name"],
                                (img["width"], img["height"]),
                                (width, height),
                            )
                        )
        if inconsistent_sizes:
            errors.append(f"图片尺寸不一致的文件: {inconsistent_sizes}")
        # else:
        #     errors.append("所有图片尺寸与标注文件一致。")
        return errors

    # 5. 检查标注边界框是否在图片范围内
    def check_bounding_boxes_in_image(self) -> list:
        errors = []
        invalid_bboxes = []
        for annotation in self.coco_data["annotations"]:
            image_info = next(
                img for img in self.coco_data["images"] if img["id"] == annotation["image_id"]
            )
            x, y, w, h = annotation["bbox"]
            if x < 0 or y < 0 or x + w > image_info["width"] or y + h > image_info["height"]:
                invalid_bboxes.append(
                    (
                        f"\"id\": {annotation['id']}",
                        annotation["bbox"],
                        image_info["file_name"],
                    )
                )
        if invalid_bboxes:
            errors.append(f"边界框超出图片范围的标注: {invalid_bboxes}")
        # else:
        #     errors.append("所有边界框都在图片范围内。")
        return errors

    # 6. 检查标注文件中的类别是否一致
    def check_category_consistency(self) -> list:
        errors = []
        category_ids = [cat["id"] for cat in self.coco_data["categories"]]
        invalid_categories = []
        for annotation in self.coco_data["annotations"]:
            if annotation["category_id"] not in category_ids:
                invalid_categories.append(annotation["category_id"])
        if invalid_categories:
            errors.append(f"标注文件中存在无效的类别ID: {set(invalid_categories)}")
        # else:
        #     errors.append("所有类别ID在标注文件中均有效。")
        return errors

    # 7. 检查图片文件格式是否符合COCO要求，并检查是否损坏
    def check_image_format_and_integrity(self) -> list:
        errors = []
        invalid_files = []
        for img in self.coco_data["images"]:
            img_path = os.path.join(self.images_dir, img["file_name"])
            if os.path.exists(img_path):
                try:
                    with Image.open(img_path) as image:
                        if image.format.upper() not in ["JPEG", "JPG"]:
                            invalid_files.append(img["file_name"])
                except Exception:
                    invalid_files.append(img["file_name"])
        if invalid_files:
            errors.append(f"格式不符合或损坏的图片文件: {invalid_files}")
        # else:
        #     errors.append("所有图片文件格式符合要求且未损坏。")
        return errors

    # 8. 检查 JSON 文件的结构是否符合 COCO 格式
    def check_json_structure(self) -> list:
        errors = []
        required_keys = {"images", "annotations", "categories"}
        if not required_keys.issubset(self.coco_data.keys()):
            missing_keys = required_keys - self.coco_data.keys()
            errors.append(f"JSON 文件结构不符合 COCO 格式，缺少以下字段: {missing_keys}")

        return errors

    # 9. 检查标签重复名称、标注的类别是否有空格、检查 bbox 是否=4 ignore字段是否不等于0
    def check_cat_images_rlg(self) -> list:
        # 之前脚本实现
        errors = []
        # 检查 categories 字段
        data = self.coco_data
        categories = data.get("categories", [])
        category_names = [category["name"] for category in categories]
        # 重复项检查
        duplicate_names = set([name for name in category_names if category_names.count(name) > 1])
        if duplicate_names:
            errors.append(
                f"coco_label 文件 {self.coco_file_path} 中的 categories 存在重复的名称: {duplicate_names}"
            )
        for name in category_names:
            label_error = ValidateUtils.check_illegal_label_name(name)
            if label_error:
                errors.append(
                    f"coco_label 文件 {self.coco_file_path} 中的 categories 存在非法标签："
                    + label_error
                )

        # 检查 images 字段
        images = data.get("images", [])
        image_filenames = [image["file_name"] for image in images]
        if len(set(image_filenames)) != len(image_filenames):
            duplicate_filenames = set([x for x in image_filenames if image_filenames.count(x) > 1])
            for filename in duplicate_filenames:
                ids_with_duplicate_filename = [
                    image["id"] for image in images if image["file_name"] == filename
                ]
                errors.append(
                    f"coco_label 文件中的json文件存在重复的图片名: '{filename}', 对应的 id: {ids_with_duplicate_filename}"
                )

        # 检查 annotations 字段
        annotations = data.get("annotations", [])
        image_id_to_file_name = {image["id"]: image["file_name"] for image in images}
        for annotation in annotations:
            bbox = annotation.get("bbox")
            image_id = annotation.get("image_id")
            if len(bbox) != 4:
                errors.append(f"bbox 小于四个坐标, 图片名: '{image_id_to_file_name.get(image_id)}'")
            if annotation.get("ignore") != 0:
                errors.append(f"ignore 字段不为 0, 图片名: '{image_id_to_file_name.get(image_id)}'")
        return errors

    # 10. 检查文件名格式
    @staticmethod
    def check_coco_filename_format(coco_filename: str) -> list:
        # 检查coco文件的格式
        # 有效格式：v.20240807.r_003.s_01.i_001.(p_01).img_cnt_000070.formal

        # 初始化错误列表
        errors = []
        coco_filename = os.path.basename(coco_filename)
        # 用 "." 分割文件名
        parts = coco_filename.split(".")

        # 检查是否有 10/11 个部分
        if len(parts) != 10 and len(parts) != 11:
            errors.append(f"文件名部分数量不正确，期望10个或11个部分 parts {parts}")
            return errors

        # 分别提取各部分
        if len(parts) == 10:
            p_part = None
            (
                version_part,
                date_part,
                r_part,
                s_part,
                i_part,
                img_cnt_part,
                formal_part,
                formal_number,
                coco_part,
                json_part,
            ) = parts
        else:
            (
                version_part,
                date_part,
                r_part,
                s_part,
                i_part,
                p_part,
                img_cnt_part,
                formal_part,
                formal_number,
                coco_part,
                json_part,
            ) = parts

        # 校验版本号部分
        if version_part != "v":
            errors.append(f"版本部分应为 'v'：'{version_part}'")

        # 校验第二部分（日期部分，8 位，以 2024 开头）
        if not (len(date_part) == 8 and date_part.startswith("202") and date_part.isdigit()):
            errors.append(f"日期部分不合规，应为8位数字且以202X开头：'{date_part}'")

        # 校验第三部分 (r_001 格式，数字 3 位，范围 1-999)
        if not re.match(r"^r_(\d{3})$", r_part):
            errors.append(f"r部分格式不正确，应为'r_XXX'，其中XXX为3位数字：'{r_part}'")
        elif not (1 <= int(r_part[2:]) <= 999):
            errors.append(f"r部分数值范围错误，应在1-999之间：'{r_part}'")

        # 校验第四部分 (s_02 格式，数字 2 位，范围 1-99)
        if not re.match(r"^s_(\d{2})$", s_part):
            errors.append(f"s部分格式不正确，应为's_XX'，其中XX为2位数字：'{s_part}'")
        elif not (1 <= int(s_part[2:]) <= 99):
            errors.append(f"s部分数值范围错误，应在1-99之间：'{s_part}'")

        # 校验第五部分 (i_027 格式，数字 3 位，范围 1-999)
        if not re.match(r"^i_(\d{3})$", i_part):
            errors.append(f"i部分格式不正确，应为'i_XXX'，其中XXX为3位数字：'{i_part}'")
        elif not (1 <= int(i_part[2:]) <= 999):
            errors.append(f"i部分数值范围错误，应在1-999之间：'{i_part}'")

        # 校验第六部分 (img_cnt_0000141 格式，数字 7 位，范围 > 0)
        if not re.match(r"^img_cnt_(\d{7})$", img_cnt_part):
            errors.append(
                f"img_cnt部分格式不正确，应为'img_cnt_XXXXXXX'，其中XXXXXXX为7位数字：'{img_cnt_part}'"
            )
        elif int(img_cnt_part[8:]) <= 0:
            errors.append(f"img_cnt部分数值错误，应大于0：'{img_cnt_part}'")

        # 校验第七部分 (formal 固定字符串)
        if formal_part not in ("formal", "demo"):
            errors.append(f"formal部分应为固定字符串 'formal'：'{formal_part}'")

        # 校验第八部分 (001 格式，3 位数字，范围 1-999)
        if not re.match(r"^\d{3}$", formal_number):
            errors.append(f"formal_number部分应为3位数字：'{formal_number}'")
        elif not (1 <= int(formal_number) <= 999):
            errors.append(f"formal_number部分数值范围错误，应在1-999之间：'{formal_number}'")

        # 校验第九部分 (coco 固定字符串)
        if coco_part != "coco":
            errors.append(f"coco部分应为固定字符串 'coco'：'{coco_part}'")

        # 校验第十部分 (json 固定字符串)
        if json_part != "json":
            errors.append(f"json部分应为固定字符串 'json'：'{json_part}'")

        # 如果分片标注存在，校验(p_01/02/03, 数字两位，范围1-10)
        if p_part:
            if not re.match(r"^p_(\d{2})$", p_part):
                errors.append(f"p部分格式不正确，应为'p_XX'，其中XX为2位数字：'{p_part}'")
            elif not (1 <= int(p_part[2:]) <= 10):
                errors.append(f"p部分数值范围错误，应在1-10之间：'{p_part}'")

        # 返回错误信息列表，如果没有错误，则返回空列表
        return errors

    # 11.检查iscrowd字段
    def check_iscrowd_field(self) -> list:
        """检查 coco_file 中的 annotations 字段下的 iscrowd 是否不为 0。"""
        errors = []
        annotations = self.coco_data.get("annotations", [])

        for annotation in annotations:
            if annotation.get("iscrowd", 0) != 0:
                errors.append("文件中的 annotations 中存在 iscrowd 字段不为 0 ")

        return errors

    # 12.检查和上级目录名称一致
    def check_coco_filenames_same_as_base_dir(self) -> list:
        """检查coco_label文件夹下每个json文件的文件名是否与base_dir的名称匹配"""
        assert self.base_dir is not None, "未定义images_dir的路径，请定义后再运行"

        base_name = os.path.basename(self.base_dir)
        errors = []

        for filename in os.listdir(os.path.dirname(self.coco_file_path)):
            if filename.endswith(".json"):
                # 提取文件名中除去版本号和后缀的部分
                coco_file_prefix = re.sub(r"\.\d{3}\.coco\.json$", "", filename)
                if not base_name.startswith(coco_file_prefix):
                    errors.append(f"文件 {filename} 的前缀与文件夹名称 {base_name} 不匹配")
            else:
                errors.append(f"文件 {filename} 为非法文件；只允许json文件在此路径下")

        return errors

    # 13.检查重叠框
    def check_overlapping_boxes(self) -> list:
        errors = []
        shapes = self.coco_data.get("shapes", [])
        errors.extend(ValidateUtils.check_shapes_for_errors(shapes, self.coco_file_path))
        return errors

    # 14.检查coco使用的最新raw_label生成
    def check_coco_file_generated_by_latest_raw_label_files(self) -> list:
        assert self.base_dir is not None, "未定义raw_label的路径，请定义base_dir参数后再运行"
        errors = []
        raw_label_dir = os.path.join(self.base_dir, "raw_label")
        coco_mod_time = datetime.fromtimestamp(os.path.getmtime(self.coco_file_path))
        raw_label_mod_time = ValidateUtils.get_dir_latest_modification_time(raw_label_dir)
        if raw_label_mod_time is None:
            errors.append("未找到raw_label文件夹，请检查路径是否正确")
        # 由于网速的不同可能造成误差，因此允许误差为3小时
        elif coco_mod_time < raw_label_mod_time - timedelta(hours=3):
            errors.append("coco_label文件夹的修改时间早于raw_label文件夹，请检查是否正确生成")
        return errors

    # 执行检查函数并汇总错误信息
    def run(self) -> list:
        assert self.images_dir is not None, "未定义self.images_dir的路径，请定义后再运行"
        errors = []

        # 执行各项检查并汇总错误信息
        # 检查图片文件名是否有重复
        _errors = self.check_duplicate_image_names()
        errors.extend(_errors)

        # 检查标注文件中的图片总数是否与实际图片文件一致
        _errors = self.check_image_count_consistency()
        errors.extend(_errors)

        # 检查标注文件中的图片在实际文件中是否存在
        _errors = self.check_image_files_exist()
        errors.extend(_errors)

        # 检查标注文件中的图片名合法
        _errors = self.check_image_file_names_valid()
        errors.extend(_errors)

        # 检查每个图片标注的尺寸是否与实际图片文件的尺寸一致
        # _errors = self.check_image_size_consistency()
        # errors.extend(_errors)

        # 检查标注边界框是否在图片范围内
        _errors = self.check_bounding_boxes_in_image()
        errors.extend(_errors)

        # 检查标注文件中的类别是否一致
        _errors = self.check_category_consistency()
        errors.extend(_errors)

        #  检查图片文件格式是否符合COCO要求，并检查是否损坏
        _errors = self.check_image_format_and_integrity()
        errors.extend(_errors)

        # 检查 JSON 文件的结构是否符合 COCO 格式
        _errors = self.check_json_structure()
        errors.extend(_errors)

        # 荣刘刚实现，检查标签重复名称、标注的类别是否有空格、检查 bbox 是否=4 ignore字段是否不等于0
        _errors = self.check_cat_images_rlg()
        errors.extend(_errors)

        # 检查coco文件的格式是否符合规范
        _errors = CocoValidator.check_coco_filename_format(self.coco_file_path)
        errors.extend(_errors)

        # 检查iscrowd字段
        _errors = self.check_iscrowd_field()
        errors.extend(_errors)

        if self.base_dir:
            # 检查JSON文件名与文件夹名称的一致性
            _errors = self.check_coco_filenames_same_as_base_dir()
            errors.extend(_errors)

            # 检查coco文件生成时间必须比raw_label的最晚更新时间大【最新版本】
            _errors = self.check_coco_file_generated_by_latest_raw_label_files()
            errors.extend(_errors)

        # 检查重复框
        _errors = self.check_overlapping_boxes()
        errors.extend(_errors)
        return errors

    @staticmethod
    def save_validation_errors(errors: list, output_file="coco_validation_errors.txt"):
        """
        将 COCO 验证错误列表写入文件
        :param errors: 验证错误列表（假设每个错误是字符串或字典）
        :param output_file: 输出文件名
        """
        try:
            if len(errors) == 0:
                return
            with open(output_file, "a", encoding="utf-8") as f:
                for error in errors:
                    f.write(error + "\n")

            print(f"验证错误已保存至: {output_file}")
        except Exception as e:
            print(f"保存错误文件时发生意外错误: {str(e)}")


if __name__ == "__main__":
    lines = []
    try:
        with open("文件夹列表.txt", "r", encoding="utf-8") as file:
            for line in file:
                # 去除首尾空白及换行符，且跳过空行
                cleaned_line = line.strip()
                if cleaned_line:  # 过滤空行
                    lines.append(cleaned_line)
    except Exception as e:
        print(f"读取文件时发生错误：{str(e)}")
    print(lines)

    for folder_path in lines:
        coco_label_dir = os.path.join(folder_path, "coco_label")
        for file in os.listdir(coco_label_dir):
            coco_label_file = os.path.join(coco_label_dir, file)
        images_dir = os.path.join(folder_path, "images")
        coco_validator = CocoValidator(coco_file_path=coco_label_file, images_dir=images_dir)
        errors = coco_validator.run()
        print(errors)
        # CocoValidator.save_validation_errors(errors)
