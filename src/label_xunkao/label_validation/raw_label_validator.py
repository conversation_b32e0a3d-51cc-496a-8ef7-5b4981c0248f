import os

from validate_utils.validate_utils import ValidateUtils


class RawLabelValidator:
    def __init__(self, raw_label_dir: str, images_dir: str = None):
        """
        Args:
            raw_label_dir: raw_label文件夹的路径,如/v.20240807.r_003.s_01.i_001.img_cnt_0000208.formal/raw_labels/
            images_dir: 对应的images文件夹路径, 如/v.20240807.r_003.s_01.i_001.img_cnt_0000208.formal/images/
        """

        self.raw_label_dir = raw_label_dir
        self.images_dir = images_dir

    def check_raw_label_json_format(self) -> list:
        """
        检查文件夹中是否存在非法文件
        """
        errors = []
        for subdir in os.listdir(self.raw_label_dir):
            subdir_path = os.path.join(self.raw_label_dir, subdir)
            if os.path.isdir(subdir_path) and subdir.startswith("formal"):
                for f in os.listdir(subdir_path):
                    if not f.endswith(".json"):
                        errors.append(f"'{f}' 不是 json 文件，不应该在raw_label文件夹中")
        return errors

    def verify_image_and_label_consistency(self):
        """
        验证 images 文件夹中的图片数量和 raw_label 文件夹中每个 formal 子文件夹的 JSON 文件数量是否一致，
        以及文件名称的前缀是否互相存在。
        """
        assert self.images_dir, " images_dir 未定义, 请先设置images文件夹的路径"
        errors = []

        # 获取 images 文件夹中的图片文件名
        image_files = [f for f in os.listdir(self.images_dir) if f.endswith(".jpg")]
        image_count = len(image_files)
        image_prefixes = set(f.rsplit(".", 1)[0] for f in image_files)

        # 获取 raw_label 下的所有 formal 子文件夹
        raw_label_subdirs = [
            d
            for d in os.listdir(self.raw_label_dir)
            if os.path.isdir(os.path.join(self.raw_label_dir, d))
        ]

        for subdir in raw_label_subdirs:
            # 获取每个 formal 子文件夹中的 JSON 文件
            raw_json_files = [
                f
                for f in os.listdir(os.path.join(self.raw_label_dir, subdir))
                if f.endswith(".json")
            ]
            json_count = len(raw_json_files)
            json_prefixes = set(f.rsplit(".", 1)[0] for f in raw_json_files)

            # 检查数量是否一致
            if image_count != json_count:
                errors.append(
                    f"图片数量 ({image_count}) 与 JSON 文件数量 ({json_count}) 在 {subdir} 文件夹中不一致"
                )

            # 检查前缀是否存在
            missing_prefixes = image_prefixes - json_prefixes
            if missing_prefixes:
                errors.append(
                    f"在 {subdir} 文件夹中找不到与这些图片前缀对应的 JSON 文件: {missing_prefixes}"
                )

            missing_json_prefixes = json_prefixes - image_prefixes
            if missing_json_prefixes:
                errors.append(
                    f"在 {subdir} 文件夹中找不到与这些 JSON 前缀对应的图片文件: {missing_json_prefixes}"
                )

        return errors

    def check_shapes_errors(self) -> list:
        """
        检查shapes字段中的图形错误，包含重叠和越界; 检查shapes中的非法标签
        """
        errors = []
        for raw_json_file in os.listdir(self.raw_label_dir):
            if raw_json_file.endswith(".json"):
                raw_data = ValidateUtils.load_json_file(
                    os.path.join(self.raw_label_dir, raw_json_file)
                )
                shapes = raw_data.get("shapes", [])
                errors.extend(ValidateUtils.check_shapes_for_errors(shapes, raw_json_file))
                for shape in shapes:
                    try:
                        label_name = shape["label"]
                        label_error = ValidateUtils.check_illegal_label_name(label_name)
                        if label_error:
                            errors.append(
                                f"文件 {raw_json_file}的shapes字段里的标签非法: " + label_error
                            )
                    except Exception as e:
                        errors.append(f"获取文件 {raw_json_file} 中 shapes 字段的标签报错：{e}")

        return errors

    def run(self) -> list:
        errors = []

        # 检查目录下的文件名
        _error = self.check_raw_label_json_format()
        errors.extend(_error)

        #  images 文件夹中的图片数量和 raw_label 文件夹中每个 formal 子文件夹的 JSON 文件数量是否一致
        _error = self.verify_image_and_label_consistency()
        errors.extend(_error)

        # 检查重叠框和越界问题
        _error = self.check_shapes_errors()
        errors.extend(_error)

        return errors
