# -*- coding: utf-8 -*-
# @Time    : 2024/7/8 14:55
# author zlj

import json
import random
import argparse
import os
from collections import OrderedDict
from sklearn.model_selection import train_test_split


def split_coco_dataset(json_path, train_ratio, val_ratio, test_ratio):
    # 确保比例之和为1
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-5, "Ratios must sum to 1"

    # 加载COCO JSON文件
    with open(json_path, "r", encoding="utf-8") as f:
        coco_data = json.load(f)

    # 获取所有图像ID
    image_ids = [img["id"] for img in coco_data["images"]]

    # 首先将数据集分为训练集和临时集（验证+测试）
    train_ids, temp_ids = train_test_split(image_ids, train_size=train_ratio, random_state=42)

    # 然后将临时集分为验证集和测试集
    val_ratio_adjusted = val_ratio / (val_ratio + test_ratio)
    val_ids, test_ids = train_test_split(temp_ids, train_size=val_ratio_adjusted, random_state=42)

    # 创建新的数据集
    def create_subset(ids):
        subset = OrderedDict()
        for key in coco_data.keys():
            if key not in ["categories", "images", "annotations"]:
                subset[key] = coco_data[key]

        subset["categories"] = sorted(coco_data["categories"], key=lambda x: x["id"])
        subset["images"] = sorted(
            [img for img in coco_data["images"] if img["id"] in ids],
            key=lambda x: x["id"],
        )
        subset["annotations"] = sorted(
            [ann for ann in coco_data["annotations"] if ann["image_id"] in ids],
            key=lambda x: x["id"],
        )

        return subset

    train_set = create_subset(train_ids)
    val_set = create_subset(val_ids)
    test_set = create_subset(test_ids)

    return train_set, val_set, test_set


def save_coco_subset(subset, filename):
    # 获取文件的目录路径
    directory = os.path.dirname(filename)

    # 如果目录不存在，则创建它
    if directory and not os.path.exists(directory):
        os.makedirs(directory)

    # 保存文件，使用utf-8编码，确保正确处理中文，并美化输出格式
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(subset, f, ensure_ascii=False, indent=2)

    print(f"Saved subset to {filename}")


def main():
    parser = argparse.ArgumentParser(
        description="Split COCO dataset into train, validation, and test sets"
    )
    parser.add_argument("--input_file", type=str, required=True, help="Input COCO JSON file path")
    parser.add_argument(
        "--output_dir",
        type=str,
        default=".",
        help="Output directory for split datasets (default: current directory)",
    )
    parser.add_argument(
        "--train_ratio",
        type=float,
        default=0.7,
        help="Ratio of training set (default: 0.7)",
    )
    parser.add_argument(
        "--val_ratio",
        type=float,
        default=0.2,
        help="Ratio of validation set (default: 0.2)",
    )
    parser.add_argument(
        "--test_ratio", type=float, default=0.1, help="Ratio of test set (default: 0.1)"
    )

    args = parser.parse_args()

    # 检查比例之和是否为1
    total_ratio = args.train_ratio + args.val_ratio + args.test_ratio
    if abs(total_ratio - 1.0) >= 1e-5:
        raise ValueError(f"The sum of ratios should be 1, but got {total_ratio}")

    # 拆分数据集
    train_set, val_set, test_set = split_coco_dataset(
        args.input_file, args.train_ratio, args.val_ratio, args.test_ratio
    )

    # 保存拆分后的数据集
    save_coco_subset(train_set, os.path.join(args.output_dir, "train_annotations.json"))
    save_coco_subset(val_set, os.path.join(args.output_dir, "val_annotations.json"))
    save_coco_subset(test_set, os.path.join(args.output_dir, "test_annotations.json"))

    print(f"Dataset split complete. Files saved in {args.output_dir}")


if __name__ == "__main__":
    main()
