@echo off
setlocal enabledelayedexpansion

echo Please enter the source directory (root directory to search for videos):
set /p source_dir="Source Directory: "
echo Please enter the target directory (where processed videos will be saved):
set /p target_dir="Target Directory: "

:: 检查目标目录是否存在，不存在则创建
if not exist "%target_dir%" mkdir "%target_dir%"

:: 遍历源目录及其子目录中的所有视频文件
for /r "%source_dir%" %%i in (*.mpeg, *.mp4) do (
    set "fullfilename=%%~ni.mp4"  
    set "output=!target_dir!\!fullfilename!"
    
    :: 使用ffmpeg设置输出视频帧率为每秒5帧，并输出为MP4格式
    echo Processing "%%i"...
    ffmpeg -i "%%i" -vf "fps=5" -c:v libx264 -preset veryfast -crf 23 -c:a copy "!output!" -y
    
    if errorlevel 1 (
        echo Error processing "%%i"
    ) else (
        echo Finished processing "%%i"
    )
)


echo All videos processed.
pause