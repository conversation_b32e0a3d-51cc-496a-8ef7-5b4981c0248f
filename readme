# 项目说明

提供一个数据项目的代码管理，包括数据爬虫、数据处理和数据开发等环节。

# 代码组织架构


.
├── data
│   ├── raw                # 原始数据
│   ├── processed          # 处理后的数据
│   └── output             # 输出数据和报告
├── notebooks              # Jupyter notebooks
├── src
│   ├── crawler            # 数据爬虫代码
│   ├── processing         # 数据处理代码
│   └── development        # 数据开发和分析代码
├── tests                  # 测试代码
├── requirements.txt       # 项目依赖
├── README.md              # 项目说明
├── pre-commit-config.yaml # 代码提交前的格式检查和reformat
├── pyproject.toml         # 项目中的默认参数配置