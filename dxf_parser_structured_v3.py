import ezdxf
import json
import os
import logging
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Any, Tuple, Optional
import numpy as np
from ezdxf.entities.dxfgroups import all_entities_on_same_layout

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def safe_coordinate_conversion(coord) -> List[float]:
    """安全地将坐标对象转换为列表"""
    if coord is None:
        return []
    try:
        if isinstance(coord, (list, tuple)):
            return [float(x) for x in coord]
        if hasattr(coord, 'x') and hasattr(coord, 'y'):
            z = getattr(coord, 'z', 0.0)
            return [float(coord.x), float(coord.y), float(z)]
        if hasattr(coord, '__iter__'):
            return [float(x) for x in coord]
        return [float(coord)]
    except Exception as e:
        logger.warning(f"坐标转换失败: {e}")
        return []


class OptimizedDXFParser:
    """优化的DXF解析器 - 专注于表格和图层数据提取"""

    def __init__(self, dxf_path: str):
        self.dxf_path = dxf_path
        self.doc = None
        self.all_entities = []
        self.layer_entities = defaultdict(list)

    def load_document(self) -> bool:
        """加载DXF文档"""
        try:
            self.doc = ezdxf.readfile(self.dxf_path)
            logger.info(f"成功加载DXF文件: {self.dxf_path}")
            return True
        except Exception as e:
            logger.error(f"无法读取DXF文件 {self.dxf_path}: {e}")
            return False

    def collect_all_entities(self):
        """收集所有实体并按图层分类"""
        logger.info("开始收集所有实体...")

        # 收集模型空间实体
        for entity in self.doc.modelspace():
            entity_info = self._extract_entity_info(entity, "模型空间")
            if entity_info:
                layer = entity_info["所在图层"]
                self.layer_entities[layer].append(entity_info)
                self.all_entities.append(entity_info)

        # 收集图纸空间实体
        for layout in self.doc.layouts:
            if layout.name != 'Model':
                for entity in layout:
                    entity_info = self._extract_entity_info(entity, f"图纸空间-{layout.name}")
                    if entity_info:
                        layer = entity_info["所在图层"]
                        self.layer_entities[layer].append(entity_info)
                        self.all_entities.append(entity_info)

        # 收集块定义实体
        for block in self.doc.blocks:
            if not block.name.startswith('*'):
                for entity in block:
                    entity_info = self._extract_entity_info(entity, f"块定义-{block.name}")
                    if entity_info:
                        layer = entity_info["所在图层"]
                        self.layer_entities[layer].append(entity_info)
                        self.all_entities.append(entity_info)

        logger.info(f"收集完成 - 总实体数: {len(self.all_entities)}, 图层数: {len(self.layer_entities)}")

        # 记录所有图层统计
        for layer, entities in self.layer_entities.items():
            logger.info(f"图层 {layer}: {len(entities)} 个实体")

        # 特别检查TK_BTWZ图层中是否包含"吴超"关键词
        self._check_wuchao_keyword()

    def _check_wuchao_keyword(self):
        """检查TK_BTWZ图层中的吴超关键词"""
        tk_btwz_entities = self.layer_entities.get('TK_BTWZ', [])
        if tk_btwz_entities:
            wuchao_count = 0
            for entity in tk_btwz_entities:
                text_content = entity.get("文本内容", "")
                if "吴超" in text_content:
                    wuchao_count += 1
                    logger.info(f"找到包含'吴超'的文本: {text_content}")
            logger.info(f"TK_BTWZ图层中包含'吴超'的实体数: {wuchao_count}")
        else:
            logger.warning("未找到TK_BTWZ图层")

    def _extract_entity_info(self, entity, space_name: str) -> Dict:
        """提取实体信息"""
        entity_type = entity.dxftype()

        entity_info = {
            "实体类型": entity_type,
            "所在图层": getattr(entity.dxf, 'layer', '0'),
            "颜色索引": getattr(entity.dxf, 'color', 0),
            "所在空间": space_name
        }

        # 提取坐标和内容信息
        if entity_type in ['TEXT', 'MTEXT']:
            entity_info["坐标"] = safe_coordinate_conversion(entity.dxf.insert)
            if entity_type == 'TEXT':
                entity_info["文本内容"] = getattr(entity.dxf, 'text', '')
                entity_info["文本高度"] = getattr(entity.dxf, 'height', 0)
            else:  # MTEXT
                entity_info["文本内容"] = entity.plain_text()
                entity_info["文本高度"] = getattr(entity.dxf, 'char_height', 0)
            entity_info["旋转角度"] = getattr(entity.dxf, 'rotation', 0)

        elif entity_type == 'INSERT':
            entity_info["坐标"] = safe_coordinate_conversion(entity.dxf.insert)
            entity_info["块名称"] = getattr(entity.dxf, 'name', '')
            # 提取块属性
            if hasattr(entity, 'attribs'):
                attributes = []
                for attrib in entity.attribs:
                    if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                        attributes.append({
                            "标签": getattr(attrib.dxf, 'tag', ''),
                            "值": attrib.dxf.text,
                            "坐标": safe_coordinate_conversion(attrib.dxf.insert)
                        })
                entity_info["块属性"] = attributes

        elif entity_type == 'LINE':
            entity_info["起点"] = safe_coordinate_conversion(entity.dxf.start)
            entity_info["终点"] = safe_coordinate_conversion(entity.dxf.end)

        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            vertices = []
            for vertex in entity:
                if hasattr(vertex, 'dxf'):
                    vertices.append(safe_coordinate_conversion(vertex.dxf.location))
                else:
                    vertices.append(safe_coordinate_conversion(vertex))
            entity_info["顶点"] = vertices
            entity_info["是否闭合"] = entity.is_closed

        elif entity_type == 'ATTDEF':
            entity_info["坐标"] = safe_coordinate_conversion(entity.dxf.insert)
            entity_info["文本内容"] = getattr(entity.dxf, 'text', '')
            entity_info["标签"] = getattr(entity.dxf, 'tag', '')

        return entity_info

    def detect_drawing_sheets(self) -> List[Dict]:
        """检测多个图纸区域"""
        logger.info("开始检测图纸区域...")

        # 收集所有有坐标的实体
        entities_with_coords = []
        for entity in self.all_entities:
            coords = entity.get("坐标")
            if coords and len(coords) >= 2:
                x, y = coords[0], coords[1]
                # 过滤异常坐标
                if abs(x) < 1e7 and abs(y) < 1e7 and not (x == 0 and y == 0):
                    entities_with_coords.append((x, y, entity))

        if not entities_with_coords:
            logger.warning("未找到有效坐标的实体")
            return []

        logger.info(f"找到 {len(entities_with_coords)} 个有坐标的实体")

        # 按X坐标排序
        entities_with_coords.sort(key=lambda x: x[0])
        x_coords = [coord[0] for coord in entities_with_coords]
        y_coords = [coord[1] for coord in entities_with_coords]

        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        x_range = x_max - x_min

        logger.info(f"坐标范围 - X: [{x_min:.2f}, {x_max:.2f}], Y: [{y_min:.2f}, {y_max:.2f}]")

        # 判断是否为多图纸
        if x_range < 50000:  # 单图纸阈值
            logger.info("检测到单图纸")
            return [{
                "图纸名称": "图纸1",
                "X范围": [x_min, x_max],
                "Y范围": [y_min, y_max],
                "实体数量": len(entities_with_coords)
            }]

        # 简单分割方法
        x_mid = (x_min + x_max) / 2
        left_entities = [coord for coord in entities_with_coords if coord[0] < x_mid]
        right_entities = [coord for coord in entities_with_coords if coord[0] >= x_mid]

        if len(left_entities) > 20 and len(right_entities) > 20:
            logger.info("使用简单分割检测到双图纸")
            logger.info(f"分割线X坐标: {x_mid:.2f}")

            # 检查"吴超"关键词在哪个图纸区域
            wuchao_in_left = 0
            wuchao_in_right = 0
            for coord in entities_with_coords:
                entity = coord[2]
                text_content = entity.get("文本内容", "")
                if "吴超" in text_content:
                    if coord[0] < x_mid:
                        wuchao_in_left += 1
                        logger.info(f"'吴超'文本在图纸1: {text_content} at ({coord[0]:.2f}, {coord[1]:.2f})")
                    else:
                        wuchao_in_right += 1
                        logger.info(f"'吴超'文本在图纸2: {text_content} at ({coord[0]:.2f}, {coord[1]:.2f})")

            logger.info(f"图纸1中包含'吴超'的实体数: {wuchao_in_left}")
            logger.info(f"图纸2中包含'吴超'的实体数: {wuchao_in_right}")

            return [
                {
                    "图纸名称": "图纸1",
                    "X范围": [min(coord[0] for coord in left_entities), max(coord[0] for coord in left_entities)],
                    "Y范围": [min(coord[1] for coord in left_entities), max(coord[1] for coord in left_entities)],
                    "实体数量": len(left_entities)
                },
                {
                    "图纸名称": "图纸2",
                    "X范围": [min(coord[0] for coord in right_entities), max(coord[0] for coord in right_entities)],
                    "Y范围": [min(coord[1] for coord in right_entities), max(coord[1] for coord in right_entities)],
                    "实体数量": len(right_entities)
                }
            ]

        # 默认单图纸
        logger.info("默认为单图纸")
        return [{
            "图纸名称": "图纸1",
            "X范围": [x_min, x_max],
            "Y范围": [y_min, y_max],
            "实体数量": len(entities_with_coords)
        }]

    def parse_sheet_content(self, sheet: Dict) -> Dict:
        """解析图纸内容"""
        logger.info(f"开始解析 {sheet['图纸名称']} 的内容...")

        # 筛选属于当前图纸的实体
        sheet_entities = self._filter_entities_by_sheet(sheet)
        logger.info(f"{sheet['图纸名称']} 包含 {len(sheet_entities)} 个实体")

        # 按图层分组
        sheet_layers = defaultdict(list)
        for entity in sheet_entities:
            layer = entity["所在图层"]
            sheet_layers[layer].append(entity)

        # 记录关键图层
        key_layers = ['TK_BTXT', 'SYST', 'TK_BTWZ']
        for layer in key_layers:
            count = len(sheet_layers.get(layer, []))
            if count > 0:
                logger.info(f"{sheet['图纸名称']} - {layer}图层: {count} 个实体")
            else:
                logger.warning(f"{sheet['图纸名称']} - 未找到{layer}图层实体")

        # 解析表格
        tables = self._parse_tables(sheet_entities, sheet_layers)

        # 主图内容（排除表格相关图层）
        main_content = self._parse_main_content(sheet_entities, sheet_layers)

        return {
            "主图": main_content,
            "表格": tables,
            "图层统计": {layer: len(entities) for layer, entities in sheet_layers.items()}
        }

    def _filter_entities_by_sheet(self, sheet: Dict) -> List[Dict]:
        """筛选属于指定图纸的实体"""
        sheet_entities = []
        x_range = sheet["X范围"]
        y_range = sheet["Y范围"]

        for entity in self.all_entities:
            coords = entity.get("坐标")
            if coords and len(coords) >= 2:
                x, y = coords[0], coords[1]
                if x_range[0] <= x <= x_range[1] and y_range[0] <= y <= y_range[1]:
                    sheet_entities.append(entity)
            else:
                # 对于没有坐标的实体，也包含进来
                sheet_entities.append(entity)

        return sheet_entities

    def _parse_tables(self, sheet_entities: List[Dict], sheet_layers: Dict) -> List[Dict]:
        """解析表格"""
        logger.info("开始解析表格...")
        tables = []

        # 解析SYST图层的图表数据
        syst_entities = sheet_layers.get('SYST', [])
        if syst_entities:
            logger.info(f"解析SYST图层表格，包含 {len(syst_entities)} 个实体")
            syst_table = self._parse_syst_table(syst_entities)
            if syst_table:
                tables.append(syst_table)

        # 解析TK_BTWZ图层数据
        text_entities = sheet_layers.get('TK_BTWZ', [])
        frame_entities = sheet_layers.get('TK_BTXT', [])

        if text_entities:
            logger.info(f"解析TK_BTWZ图层数据，包含 {len(text_entities)} 个实体")

            # 添加TK_BTWZ图层的原始数据输出
            tk_btwz_data = self._extract_tk_btwz_data(text_entities)
            if tk_btwz_data:
                tables.append(tk_btwz_data)

        logger.info(f"共解析出 {len(tables)} 个表格")
        return tables

    def _parse_syst_table(self, syst_entities: List[Dict]) -> Optional[Dict]:
        """解析SYST图层的表格数据"""
        text_entities = [e for e in syst_entities if e.get("文本内容")]
        if not text_entities:
            return None

        # 按坐标组织文本
        chart_data = []
        for entity in text_entities:
            coords = entity.get("坐标", [])
            if len(coords) >= 2:
                chart_data.append({
                    "x": coords[0],
                    "y": coords[1],
                    "text": entity["文本内容"].strip()
                })

        if not chart_data:
            return None

        # 按Y坐标分组（行）
        chart_data.sort(key=lambda x: -x["y"])  # 从上到下
        rows = []
        current_row = []
        current_y = None
        tolerance = 50  # 行间距容差

        for item in chart_data:
            if current_y is None or abs(item["y"] - current_y) > tolerance:
                if current_row:
                    current_row.sort(key=lambda x: x["x"])  # 按X坐标排序
                    rows.append([t["text"] for t in current_row])
                current_row = [item]
                current_y = item["y"]
            else:
                current_row.append(item)

        if current_row:
            current_row.sort(key=lambda x: x["x"])
            rows.append([t["text"] for t in current_row])

        return {
            "表格名称": "SYST图表数据",
            "表格类型": "SYST图层",
            "行数": len(rows),
            "列数": max(len(row) for row in rows) if rows else 0,
            "表格数据": rows,
            "原始实体数": len(syst_entities),
            "原始实体信息": syst_entities  # 添加完整的原始实体信息
        }

    def _extract_tk_btwz_data(self, text_entities: List[Dict]) -> Optional[Dict]:
        """提取TK_BTWZ图层的数据"""
        logger.info("提取TK_BTWZ图层数据...")

        raw_texts = []
        wuchao_texts = []

        # 记录所有实体的坐标范围，用于调试
        x_coords = []
        y_coords = []

        for entity in text_entities:
            text_content = entity.get("文本内容", "").strip()
            coords = entity.get("坐标", [])

            # 收集坐标用于分析
            if len(coords) >= 2:
                x_coords.append(coords[0])
                y_coords.append(coords[1])

            if text_content:
                text_info = {
                    "内容": text_content,
                    "坐标": coords,
                    "实体类型": entity.get("实体类型", ""),
                    "所在图层": entity.get("所在图层", ""),
                    "所在空间": entity.get("所在空间", "")
                }
                raw_texts.append(text_info)

                # 特别收集包含"吴超"的文本
                if "吴超" in text_content:
                    wuchao_texts.append(text_info)
                    logger.info(f"在当前图纸中找到包含'吴超'的文本: {text_content} at ({coords[0] if len(coords) >= 2 else 'N/A'}, {coords[1] if len(coords) >= 2 else 'N/A'})")

        if not raw_texts:
            return None

        # 记录坐标范围用于调试
        coord_range = {}
        if x_coords and y_coords:
            coord_range = {
                "X范围": [min(x_coords), max(x_coords)],
                "Y范围": [min(y_coords), max(y_coords)]
            }
            logger.info(f"TK_BTWZ实体坐标范围: X[{min(x_coords):.2f}, {max(x_coords):.2f}], Y[{min(y_coords):.2f}, {max(y_coords):.2f}]")

        # 按Y坐标排序（从上到下）
        raw_texts.sort(key=lambda x: -x["坐标"][1] if len(x["坐标"]) >= 2 else 0)

        return {
            "表格名称": "TK_BTWZ图层数据",
            "表格类型": "TK_BTWZ文本数据",
            "文本总数": len(raw_texts),
            "包含吴超的文本数": len(wuchao_texts),
            "吴超相关文本": wuchao_texts,
            "坐标范围": coord_range,
            "所有文本数据": raw_texts,  # 输出所有文本数据，不限制数量
            "原始实体信息": text_entities,  # 添加完整的原始实体信息
            "数据统计": {
                "总文本数": len(raw_texts),
                "有坐标文本数": len([t for t in raw_texts if len(t["坐标"]) >= 2]),
                "非空文本数": len([t for t in raw_texts if t["内容"]])
            }
        }

    def _reconstruct_table_from_text_and_frame(self, text_entities: List[Dict], frame_entities: List[Dict]) -> Optional[Dict]:
        """从文字和线框重建表格（简化版本）"""
        # 提取所有线段
        lines = []
        for entity in frame_entities:
            if entity["实体类型"] == "LINE":
                start = entity.get("起点", [])
                end = entity.get("终点", [])
                if len(start) >= 2 and len(end) >= 2:
                    lines.append({"start": start, "end": end})

        if not lines:
            logger.warning("未找到有效的线框数据")
            return None

        # 构建简化的表格结构
        table_texts = []
        for entity in text_entities:
            text_content = entity.get("文本内容", "").strip()
            coords = entity.get("坐标", [])
            if text_content and len(coords) >= 2:
                table_texts.append({
                    "text": text_content,
                    "x": coords[0],
                    "y": coords[1]
                })

        # 按坐标排序组织成行列
        table_texts.sort(key=lambda x: (-x["y"], x["x"]))  # 先按Y降序，再按X升序

        return {
            "表格名称": "TK重建表格",
            "表格类型": "TK文字线框表格",
            "文本数量": len(table_texts),
            "线段数量": len(lines),
            "表格文本": [t["text"] for t in table_texts[:100]]  # 限制显示数量
        }

    def _parse_main_content(self, sheet_entities: List[Dict], sheet_layers: Dict) -> Dict:
        """解析主图内容"""
        # 排除表格相关的图层
        exclude_layers = {'TK_BTXT', 'TK_BTWZ', 'SYST'}

        main_layers = {}
        total_entities = 0
        all_entities=[]
        for layer, entities in sheet_layers.items():
            if layer not in exclude_layers:
                main_layers[layer] = len(entities)
                total_entities += len(entities)
                all_entities.extend(entities)

        return {
            "图层统计": main_layers,
            "总实体数": total_entities,
            "主要图层": sorted(main_layers.items(), key=lambda x: x[1], reverse=True)[:10],
            "all_entities": all_entities
        }

    def get_all_layers_detail(self) -> Dict:
        """获取所有图层的详细信息"""
        layers_detail = {}

        for layer_name, entities in self.layer_entities.items():
            layer_info = {
                "实体数量": len(entities),
                "实体类型统计": {},
                "文本内容": []
            }

            # 统计实体类型
            for entity in entities:
                entity_type = entity.get("实体类型", "未知")
                layer_info["实体类型统计"][entity_type] = layer_info["实体类型统计"].get(entity_type, 0) + 1

                # 收集文本内容
                text_content = entity.get("文本内容", "")
                if text_content and text_content.strip():
                    layer_info["文本内容"].append({
                        "内容": text_content.strip(),
                        "坐标": entity.get("坐标", []),
                        "实体类型": entity_type
                    })

            # 特别标记包含"吴超"的图层
            # wuchao_texts = [t for t in layer_info["文本内容"] if "吴超" in t["内容"]]
            # if wuchao_texts:
            #     layer_info["包含吴超关键词"] = True
            #     layer_info["吴超相关文本"] = wuchao_texts

            layers_detail[layer_name] = layer_info

        return layers_detail

    def generate_output(self) -> Dict:
        """生成最终输出"""
        if not self.load_document():
            return {"错误": "无法加载DXF文件"}

        # 收集所有实体
        self.collect_all_entities()

        # 检测图纸区域
        drawing_sheets = self.detect_drawing_sheets()

        # 解析每个图纸的内容
        result = {
            "文件名": os.path.basename(self.dxf_path),
            "DXF版本": self.doc.dxfversion,
            "总图层数": len(self.layer_entities),
            "总实体数": len(self.all_entities),
            "图纸数量": len(drawing_sheets),
            "所有图层详情": self.get_all_layers_detail()
        }

        for sheet in drawing_sheets:
            sheet_content = self.parse_sheet_content(sheet)
            result[sheet["图纸名称"]] = sheet_content

        return result


def process_dxf_files(input_path: str, output_dir: str = None):
    """处理DXF文件"""
    input_path = Path(input_path)

    if not input_path.exists():
        raise FileNotFoundError(f"输入路径不存在: {input_path}")

    # 获取DXF文件列表
    if input_path.is_file():
        dxf_files = [input_path] if input_path.suffix.lower() == '.dxf' else []
    else:
        dxf_files = list(input_path.rglob('*.dxf')) + list(input_path.rglob('*.DXF'))

    if not dxf_files:
        raise ValueError("未找到DXF文件")

    # 创建输出目录
    if not output_dir:
        output_dir = input_path.parent / f"{input_path.stem}_parsed_optimized"
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"找到 {len(dxf_files)} 个DXF文件")
    logger.info(f"输出目录: {output_dir}")

    results = {"成功": 0, "失败": 0, "总计": len(dxf_files), "失败文件": []}

    for dxf_file in dxf_files:
        try:
            logger.info(f"开始处理: {dxf_file.name}")

            parser = OptimizedDXFParser(str(dxf_file))
            result = parser.generate_output()

            if "错误" in result:
                results["失败"] += 1
                results["失败文件"].append(str(dxf_file))
                logger.error(f"解析失败: {dxf_file.name} - {result['错误']}")
                continue

            # 保存结果
            output_file = output_dir / f"{dxf_file.stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            results["成功"] += 1
            logger.info(f"✓ 成功解析: {dxf_file.name}")

        except Exception as e:
            results["失败"] += 1
            results["失败文件"].append(str(dxf_file))
            logger.error(f"✗ 解析异常: {dxf_file.name} - {e}")

    logger.info(f"解析完成 - 成功: {results['成功']}, 失败: {results['失败']}")
    return results


if __name__ == '__main__':
    # 示例用法---`
    # input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化_test/'
    input_path = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/2025 市场项目/中广核/图纸格式转化/BJ0EEX96011DETX43MD11CCFC0BEE火灾自动报警系统施工图图纸目录3.dxf'

    output_dir = './optimized_output'

    process_dxf_files(input_path, output_dir)
